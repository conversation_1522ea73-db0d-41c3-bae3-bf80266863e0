import mysql.connector
import logging
from mysql.connector import Error

# === Logging Setup ===
logging.basicConfig(
    filename='deployer_tous.log',
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
)

# === Database Configuration ===
khronos_config = {
    "user": "prodops",
    "password": "prodops",
    "host": "FRSOPSLAPPV55",
    "port": "3306",
    "database": "khronos",
    "raise_on_warnings": True,
}

# === Clone Function ===
def clone_table_structure(source_table, target_table):
    try:
        logging.info(f"Connecting to database '{khronos_config['database']}' on {khronos_config['host']}...")
        conn = mysql.connector.connect(**khronos_config)

        if conn.is_connected():
            logging.info("Connection established.")
            cursor = conn.cursor()

            # Check if target table already exists
            logging.info(f"Checking if table `{target_table}` already exists...")
            check_query = (
                "SELECT COUNT(*) FROM information_schema.tables "
                "WHERE table_schema = %s AND table_name = %s"
            )
            cursor.execute(check_query, (khronos_config['database'], target_table))
            (table_exists,) = cursor.fetchone()

            if table_exists:
                logging.info(f"Table `{target_table}` already exists. Skipping creation.")
            else:
                logging.info(f"Creating table `{target_table}` LIKE `{source_table}`...")
                cursor.execute(f"CREATE TABLE `{target_table}` LIKE `{source_table}`;")
                conn.commit()
                logging.info(f"Table `{target_table}` created successfully.")

    except Error as e:
        logging.error(f"MySQL error: {e}")
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
    finally:
        if 'conn' in locals() and conn.is_connected():
            conn.close()
            logging.info("Connection closed.")

# === Run Script ===
if __name__ == "__main__":
    clone_table_structure("khronos_data_containers", "khronos_data_containers_v2")

# coding=utf-8
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.support import expected_conditions as EC
import sys
import time
import subprocess
from selenium.webdriver.support import expected_conditions as EC
import requests
from requests.packages import urllib3
from time import time
from modules.perform_transaction_1 import perform_transaction_1
from modules.perform_transaction_2 import perform_transaction_2
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# TODO 2: manque temps d'attentes dans les fonctions

class TestTest1:
    def url_down_output(self):
        # TODO to verify if this output is compatible with db
        print("transaction1=0")
        print("ac_time1=0.00")
        print("transaction2=0")
        print("ac_time2=0.00")
        print("transaction3=0")
        print("ac_time3=0.00")
        print("transaction4=0")
        print("ac_time4=0.00")
        print("transaction5=0")
        print("ac_time5=0.00")
        print("transaction6=0")
        print("ac_time6=0.00")
        print("transaction7=0")
        print("ac_time7=0.00")
        print("transaction8=0")
        print("ac_time8=0.00")                        
        print("scenario=0")
        print("scenariotime=0.0")

    def affichage_tr(self, n, transaction_n_state, transaction_n_time):
        print("transaction{}={:.1f}".format(n, transaction_n_state))
        print("ac_time{}={:.2f}".format(n, transaction_n_time))

    def affichage_av(self, scenario, totale):
        print("scenario={:.1f}".format(scenario))
        print("scenariotime={:.2f}".format(totale))

    def setup_method(self, method):
        #! using headless firefox
        self.options = FirefoxOptions()
        self.options.add_argument("--headless")
        self.driver = webdriver.Firefox(
            options=self.options
        )
        self.driver.implicitly_wait(20)
        self.vars = {}

    def teardown_method(self):
        self.driver.quit()

    def test_test1(self):
        # Test name: covea_1
        # Step # | name | target | value
        #! les temps d'attente
        temps_attente_tr1 = 3
        temps_attente_tr2 = 5
        temps_attente_tr3 = 1
        temps_attente_tr4 = 5
        temps_attente_tr5 = 3
        temps_attente_tr6 = 3
        temps_attente_tr7 = 5
        temps_attente_tr8 = 3
        temps_saisie = 16
        from time import time

        #! transaction_1
        #! Demander l’URL page de l’application pl pur
        start_time = time()
        transaction_1_state, transaction_1_time = perform_transaction_1(self, "https://ple5-covea.soprahronline.sopra/app/foryou/#/login")
        if transaction_1_state != 0:
            self.affichage_tr(1, transaction_1_state, transaction_1_time)
        else:
            self.driver.quit()
            sys.exit(0)        
        #! transaction_2
        #! Saisir les champs suivants :
        #! Identifiant 	LOGIN Utilisateur A056825
        #! Mot de passe 	Password Utilisateur 411271
        #! avec profil gestionnaire
        #! Cliquer sur le bouton « Me connecter »
        start_time = time()
        # Define window size and other parameters
        window_size = (1271, 690)
        login_id = "loginInput"
        password_id = "passwordInput"
        submit_button_selector = ".ladda-label"
        # Perform transaction 2 by calling the imported function
        transaction_2_state, transaction_2_time = perform_transaction_2(self.driver, window_size, login_id, password_id, submit_button_selector)
        if transaction_2_state != 0:
            self.affichage_tr(2, transaction_2_state, transaction_2_time)
        else:
            self.driver.quit()
            sys.exit(0)                
        # try:
        #     # 2 | setWindowSize | 1271x690 |
        #     self.driver.set_window_size(1271, 690)
        #     try:
        #         # 3 | type | id=loginInput | A056825
        #         wait = WebDriverWait(self.driver, 99)
        #         element = wait.until(EC.element_to_be_clickable((By.ID, "loginInput")))
        #         element.send_keys("SUPERVISION")
        #     except:
        #         print("element ID=loginInput introuvable!")
        #         self.driver.quit()
        #         sys.exit(0)
        #     try:
        #         # 4 | type | id=passwordInput | 411271
        #         wait = WebDriverWait(self.driver, 99)
        #         element = wait.until(
        #             EC.element_to_be_clickable((By.ID, "passwordInput"))
        #         )
        #         element.send_keys("S0PR4")
        #     except:
        #         print("element ID=passwordInput introuvable!")
        #         self.driver.quit()
        #         sys.exit(0)
        #     try:
        #         # 5 | click | css=.ladda-label |
        #         wait = WebDriverWait(self.driver, 99)
        #         element = wait.until(
        #             EC.element_to_be_clickable((By.CSS_SELECTOR, ".ladda-label"))
        #         )
        #         element.click()
        #     except:
        #         print("element CSS_SELECTOR=.ladda-label introuvable!")
        #         self.driver.quit()
        #         sys.exit(0)
        # except:
        #     transaction_2_state = 0
        #     print("transaction1={:.1f}".format(transaction_1_state))
        #     self.driver.quit()
        #     sys.exit(0)
        # transaction_2_state = 1
        # end_time = time()
        # if transaction_2_state != 0:
        #     transaction_2_time = round((end_time - start_time), 3)
        #     transaction_2_time = transaction_2_time + temps_attente_tr2
        #     self.affichage_tr(2, transaction_2_state, transaction_2_time)
        # else:
        #     self.driver.quit()
        #     sys.exit(0)
        #! transaction_3
        #! Cliquer sur la loupe de recherche
        start_time = time()
        try:
            # # 6 | click | css=.search_icon |
            # wait = WebDriverWait(self.driver, 99)
            # element = wait.until(
            #     EC.element_to_be_clickable((By.CSS_SELECTOR, ".search_icon"))
            # )
            # element.click()
            transaction_3_state = 1
        except:
            print("element CSS_SELECTOR=.search_icon introuvable!")
            transaction_3_state = 0
            print("transaction3={:.1f}".format(transaction_3_state))
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_3_state != 0:
            transaction_3_time = round((end_time - start_time), 3)
            transaction_3_time = transaction_3_time + temps_attente_tr3
            self.affichage_tr(3, transaction_3_state, transaction_3_time)
        else:
            self.driver.quit()
            exit(0)
        #! transaction_4
        #! Saisir « Comptes bancaires SEPA »
        #! puis sélectionner le résultat en cliquant dessus
        start_time = time()
        try:
            # try:
            #     # 7 | click | //*[@id="navbarInputSearch"]/a[1] |
            #     wait = WebDriverWait(self.driver, 99)
            #     element = wait.until(
            #         EC.element_to_be_clickable(
            #             (By.XPATH, '//*[@id="navbarInputSearch"]/a[1]')
            #         )
            #     )
            #     element.click()
            # except:
            #     print('element XPATH=//*[@id="navbarInputSearch"]/a[1] introuvable!')
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     # 8 | click | id=navBarSearchTextId |
            #     wait = WebDriverWait(self.driver, 99)
            #     element = wait.until(
            #         EC.element_to_be_clickable((By.ID, "navBarSearchTextId"))
            #     )
            #     element.click()
            # except:
            #     print("element ID=navBarSearchTextId introuvable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     # 9 | type | id=navBarSearchTextId | comptes bancaires SEPA
            #     wait = WebDriverWait(self.driver, 99)
            #     element = wait.until(
            #         EC.element_to_be_clickable((By.ID, "navBarSearchTextId"))
            #     )
            #     element.send_keys("comptes bancaires SEPA")
            # except:
            #     print("element ID=navBarSearchTextId introuvable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     # 10 | click | xpath=//b[contains(.,'Comptes bancaires SEPA')] |
            #     wait = WebDriverWait(self.driver, 99)
            #     element = wait.until(
            #         EC.element_to_be_clickable(
            #             (By.XPATH, "//b[contains(.,'Comptes bancaires SEPA')]")
            #         )
            #     )
            #     element.click()
            # except:
            #     print(
            #         "element XPATH=//b[contains(.,'Comptes bancaires SEPA')] introuvable!"
            #     )
            #     self.driver.quit()
            #     sys.exit(0)
            transaction_4_state = 1
        except:
            transaction_4_state = 0
            print("transaction4={:.1f}".format(transaction_4_state))
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_4_state != 0:
            transaction_4_time = round((end_time - start_time), 3)
            transaction_4_time = transaction_4_time + temps_attente_tr4
            self.affichage_tr(4, transaction_4_state, transaction_4_time)
        else:
            self.driver.quit()
            exit(0)
        #! transaction_5
        #!  sélectionner le rôle « Gestionnaire Niveau 1»
        #! La page de recherche s’affiche
        start_time = time()
        try:
            # try:
            #     # 11 | click | xpath=//select |
            #     wait = WebDriverWait(self.driver, 99)
            #     search = wait.until(
            #         EC.presence_of_element_located((By.XPATH, "//select"))
            #     )
            #     action = ActionChains(self.driver)
            #     action.move_to_element(search).perform()
            # except:
            #     print("element XPATH=//select introuvable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     # 11,5 | click | xpath=//select | Click
            #     action.click()
            # except:
            #     print("element XPATH=//select incliquable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     # 12 | select | xpath=//select | label=Gestionnaire Niveau 1
            #     wait = WebDriverWait(self.driver, 99)
            #     search = wait.until(
            #         EC.presence_of_element_located(
            #             (By.XPATH, "//option[. = 'Gestionnaire Niveau 1']")
            #         )
            #     )
            #     action = ActionChains(self.driver)
            #     action.move_to_element(search).perform()
            # except:
            #     print(
            #         "element XPATH=//option[. = 'Gestionnaire Niveau 1'] introuvable!"
            #     )
            # try:
            #     # 12,5 | select | xpath=//select | label=Gestionnaire Niveau 1 | click
            #     action.click()
            # except:
            #     print(
            #         "element  XPATH=//option[. = 'Gestionnaire Niveau 1'] incliquable!"
            #     )
            #     self.driver.quit()
            #     sys.exit(0)
            transaction_5_state = 1
        except:
            transaction_5_state = 0
            print("transaction5={:.1f}".format(transaction_5_state))
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_5_state != 0:
            transaction_5_time = round((end_time - start_time), 3)
            transaction_5_time = transaction_5_time + temps_attente_tr5
            self.affichage_tr(5, transaction_5_state, transaction_5_time)
        else:
            self.driver.quit()
            exit(0)
        #! transaction_6
        #! Dans le champ indiqué ci-dessous :
        #! Matricule contient : saisir « 00410158 »
        #! Cliquer sur le bouton « Rechercher »
        #! Les résultats de recherche s’affichent.
        start_time = time()
        try:
            # # 14 | selectFrame | index=0 |
            # self.driver.switch_to.frame(0)
            # try:
            #     # 15 | click | id=rSalarie2_matCollab |
            #     wait = WebDriverWait(self.driver, 99)
            #     element = wait.until(
            #         EC.element_to_be_clickable((By.ID, "rSalarie2_matCollab"))
            #     )
            # except:
            #     print("element ID=rSalarie2_matCollab introuvable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     element.click()
            # except:
            #     print("element ID=rSalarie2_matCollab incliquable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # # try:
            # #     # 16 | type | id=rSalarie2_matCollab | 00410158
            # #     wait = WebDriverWait(self.driver, 99)
            # #     element = wait.until(
            # #         EC.element_to_be_clickable((By.ID, "rSalarie2_matCollab"))
            # #     )
            # # except:
            # #     print("element ID=rSalarie2_matCollab introuvable!")
            # #     self.driver.quit()
            # #     sys.exit(0)
            # try:
            #     element.send_keys("00410158")
            # except:
            #     print('valeur element.send_keys="00410158" incliquable!')
            #     self.driver.quit()
            #     sys.exit(0)
            transaction_6_state = 1
        except:
            transaction_6_state = 0
            print("transaction6={:.1f}".format(transaction_6_state))
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_6_state != 0:
            transaction_6_time = round((end_time - start_time), 3)
            transaction_6_time = transaction_6_time + temps_attente_tr6
            self.affichage_tr(6, transaction_6_state, transaction_6_time)
        else:
            self.driver.quit()
            exit(0)
        #! transaction_7
        #! Cliquer sur le premier matricule de la liste
        #! La transaction de la liste de comptes bancaires s’affiche
        start_time = time()
        try:
            # try:
            #     # 17 | click | id=rSalarie2_EButton_0 |
            #     wait = WebDriverWait(self.driver, 99)
            #     element = wait.until(
            #         EC.element_to_be_clickable((By.ID, "rSalarie2_EButton_0"))
            #     )
            # except:
            #     print("element ID=rSalarie2_EButton_0 introuvable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     # 17,5 | click | id=rSalarie2_EButton_0 | click
            #     element.click()
            # except:
            #     print("element ID=rSalarie2_EButton_0 incliquable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     # 18 | click | linkText=00410158 |
            #     wait = WebDriverWait(self.driver, 99)
            #     search = wait.until(
            #         EC.presence_of_element_located((By.XPATH, "//tr[2]/td[2]/a"))
            #     )
            #     action = ActionChains(self.driver)
            #     action.move_to_element(search)
            # except:
            #     print("element XPATH=//a[contains(text(),'00410158')] introuvable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     # 19,5 click | xpath=//tr[2]/td[2]/a | click
            #     element.click()
            # except:
            #     print("element action.click() unclickable!")
            #     self.driver.quit()
            #     sys.exit(0)
            transaction_7_state = 1
        except:
            transaction_7_state = 0
            print("transaction7={:.1f}".format(transaction_7_state))
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_7_state != 0:
            transaction_7_time = round((end_time - start_time), 3)
            transaction_7_time = transaction_7_time + temps_attente_tr7
            self.affichage_tr(7, transaction_7_state, transaction_7_time)
        else:
            self.driver.quit()
            sys.exit(0)
        #! transaction_8
        #! Cliquer sur le lien « Se déconnecter »
        #! L’utilisateur est déconnecté de l’application
        #! La page d’accueil de l’application s’affiche
        start_time = time()
        try:
            # # 18 | selectFrame | relative=parent |
            # self.driver.switch_to.default_content()
            # try:
            #     # 19 | click | id=navbar-logout-link-id |
            #     wait = WebDriverWait(self.driver, 99)
            #     element = wait.until(
            #         EC.element_to_be_clickable((By.ID, "navbar-logout-link-id"))
            #     )
            # except:
            #     print("element ID=navbar-logout-link-id introuvable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     # 19,5 | click | id=navbar-logout-link-id | Click
            #     element.click()
            # except:
            #     print("element ID=navbar-logout-link-id incliquable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # # 20 | close |  |
            # self.driver.close()
            transaction_8_state = 1
        except:
            transaction_8_state = 0
            print("transaction8={:.1f}".format(transaction_8_state))
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_8_state != 0:
            transaction_8_time = round((end_time - start_time), 3)
            transaction_8_time = transaction_8_time + temps_attente_tr8
            self.affichage_tr(8, transaction_8_state, transaction_8_time)
        else:
            self.driver.quit()
            exit(0)
        scenario = transaction_8_state
        totale = (
            transaction_1_time
            + transaction_2_time
            + transaction_3_time
            + transaction_4_time
            + transaction_5_time
            + transaction_6_time
            + transaction_7_time
            + transaction_8_time
        ) / 2 + temps_saisie
        print("scenario={:.1f}".format(scenario))
        print("scenariotime={:.2f}".format(totale))

    def perf_mesaure(self):
        self.setup_method(self)
        self.test_test1()
        self.teardown_method()


t = TestTest1()
t.perf_mesaure()
t.teardown_method()


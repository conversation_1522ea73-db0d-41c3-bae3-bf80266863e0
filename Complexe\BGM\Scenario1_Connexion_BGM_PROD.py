#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
    output_failure_results,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["bgm"]["username"], credentials["bgm"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-prodt-bgm.soprahronline.sopra/pleiades/"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Load credentials
try:
    username, password = load_credentials()
except Exception as e:
    logging.error(f"Failed to load credentials: {e}")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    #! transaction_1: Navigate to BGM Pleiades
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to BGM Pleiades",
        measure_time=True,
        unique_str="step1",
        total_steps=1,
    )

    #! transaction_2: Switch to frame and prepare for login
    check_and_log(
        test.driver,
        test.switch_to_frame,
        "Step 2: Switch to frame",
        measure_time=True,
        unique_str="step2",
        total_steps=1,
    )

    # Step 3: Take screenshot before login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 3: Screenshot before login"),
        "Step 3: Take screenshot before login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_3: Enter credentials
    # Step 4: Locate and enter username
    username_field = check_and_log(
        test.driver,
        test.locate_bgm_username_field,
        "Step 4: Locate username field",
        measure_time=True,
        unique_str="step456",
        total_steps=3,
    )

    check_and_log(
        test.driver,
        lambda: username_field.send_keys(username),
        "Step 5: Enter username",
        measure_time=True,
        unique_str="step456",
    )

    # Step 6: Locate and enter password
    password_field = check_and_log(
        test.driver,
        test.locate_bgm_password_field,
        "Step 6: Locate and enter password",
        measure_time=True,
        unique_str="step456",
    )

    check_and_log(
        test.driver,
        lambda: password_field.send_keys(password),
        "Step 6: Enter password",
        measure_time=True,
        unique_str="step456",
    )

    #! transaction_4: Login and verify success
    check_and_log(
        test.driver,
        test.click_bgm_login_button,
        "Step 7: Click login button",
        measure_time=True,
        unique_str="step7",
        total_steps=1,
    )

    # Step 8: Take screenshot after successful login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 8: Screenshot after successful login"),
        "Step 8: Take screenshot after successful login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_5: Logout
    check_and_log(
        test.driver,
        test.click_bgm_exit_button,
        "Step 9: Click exit button",
        measure_time=True,
        unique_str="step9",
        total_steps=1,
    )

    # Step 10: Take final screenshot
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 10: Final screenshot after logout"),
        "Step 10: Take final screenshot after logout",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        is_last_step=True,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")
    # Output failure results to stdout
    output_failure_results()

finally:
    # Cleanup
    test.teardown_driver()

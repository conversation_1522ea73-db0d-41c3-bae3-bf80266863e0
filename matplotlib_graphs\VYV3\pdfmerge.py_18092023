import PyPDF2
import subprocess
import os

# Get the absolute path of the current directory
current_directory = os.path.abspath(os.getcwd())
print("Current Directory:", current_directory)



# Run script1.py
subprocess.run(['python3', 'report_graphs.py'])

# Run script2.py
subprocess.run(['python3', 'report_tables.py'])


def merge_pdfs(input_pdfs, output_pdf):
    merger = PyPDF2.PdfMerger()

    for pdf in input_pdfs:
        merger.append(pdf)

    with open(output_pdf, 'wb') as output:
        merger.write(output)

    # Delete input PDF files
    for pdf in input_pdfs:
        pdf_file = open(pdf, 'rb')
        pdf_file.close()  # Close the file handle before deletion
        os.remove(pdf)

# Change working directory to /app/
os.chdir('/app/')
# List of input PDF files to merge
input_pdfs = ['/app/output/tables.pdf', '/app/output/graphs.pdf']

import datetime

# Get the current date and time
current_datetime = datetime.datetime.now()

# Format date and time components
year = current_datetime.year
month = current_datetime.month
day = current_datetime.day
hour = current_datetime.hour
minute = current_datetime.minute

# Construct the PDF file name with the formatted components
output_pdf = f'/app/output/client_nom_rapport_{year}_{month:02d}_{day:02d}_{hour:02d}_{minute:02d}.pdf'


# Call the function to merge the PDFs and delete input PDFs
merge_pdfs(input_pdfs, output_pdf)


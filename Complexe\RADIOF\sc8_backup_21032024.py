# coding=utf-8

# => ok
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.support import expected_conditions as EC
import os
import sys
import time
import subprocess
from selenium.webdriver.support import expected_conditions as EC
import requests
from requests.packages import urllib3
from time import time
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class TestSc8():
    def check_url(self):
        """
        This function checks the URL using the curl command.
        It first sends a request using the `-I` option to only retrieve the header information.
        If the response code is 0, it means the URL is reachable, so the function sends another request to retrieve the page source.
        The page source is saved to a file `temp_ple5_adp_axa_page_source.html`.
        The function then opens the file and reads its content.
        If the string `"logosoprahr"` is found in the content, the function returns True.
        If the response code from the first request is not 0 or the string `"logosoprahr"` is not found in the content, the function returns False.
        """
        with open(os.devnull, 'w') as devnull:
            response = subprocess.call(
                ['curl', '-I', 'https://ple5-radiofrance.soprahronline.sopra/app/foryou/', '--insecure'], stdout=devnull, stderr=devnull)
        if response == 0:
            temp_ple5_adp_axa_page_source = "temp_ple5_adp_axa_page_source.html"
            with open(os.devnull, 'w') as devnull:
                subprocess.call(
                    [
                        'curl',
                        'https://ple5-radiofrance.soprahronline.sopra/app/foryou/',
                        '--insecure'
                    ],
                    stdout=open(temp_ple5_adp_axa_page_source, 'w'),
                    stderr=devnull)
            with open(temp_ple5_adp_axa_page_source, "r") as f:
                content = f.read()
                if "logosoprahr" in content:
                    return True
                else:
                    return False
        else:
            return False

    def url_down_output(self):
        # TODO to verify if this output is compatible with db
        print("transaction1=0")
        print("ac_time1=0.00")
        print("transaction2=0")
        print("ac_time2=0.00")
        print("transaction3=0")
        print("ac_time3=0.00")
        print("transaction4=0")
        print("ac_time4=0.00")
        print("transaction5=0")
        print("ac_time5=0.00")
        print("transaction6=0")
        print("ac_time6=0.00")
        print("transaction7=0")
        print("ac_time7=0.00")         
        print("scenario=0")
        print("scenariotime=0.0")

    def affichage_tr(self, n, transaction_n_state, transaction_n_time):
        print("transaction{}={:.1f}".format(n, transaction_n_state))
        print("ac_time{}={:.2f}".format(n, transaction_n_time))

    def affichage_av(self, scenario, totale):
        print("scenario={:.1f}".format(scenario))
        print("scenariotime={:.2f}".format(totale))

    def setup_method(self, method):
        #! using headless firefox
        self.options = FirefoxOptions()
        self.options.add_argument("--headless")
        self.driver = webdriver.Firefox(
            options=self.options
        )
        self.driver.implicitly_wait(20)
        self.vars = {}

    def teardown_method(self):
        self.driver.quit()

    def test_sc8(self):
        # Test name: sc8
        # Step # | name | target | value
        temps_attente_tr1 = 0
        temps_attente_tr2 = 0
        temps_attente_tr3 = 0
        temps_attente_tr4 = 0
        temps_attente_tr5 = 0
        temps_attente_tr6 = 0
        temps_attente_tr7 = 0
        temps_saisie = 1

        #! transaction_1
        # Demander l’URL page de l’application 4YOU hors SSO
        # Une fenêtre d’authentification s’affiche
        start_time = time()
        # ? checking av
        try:
            r = requests.head(
                "https://ple5-radiofrance.soprahronline.sopra/app/foryou/",
                verify=False,
                allow_redirects=True
            )
            if (r.status_code == 200 or r.status_code == 302):
                transaction_1_state = 1
            else:
                transaction_1_state = 0
                totale = 0
                print("scenario={:.1f}".format(transaction_1_state))
                print("scenariotime={:.2f}".format(totale))
        except:
            transaction_1_state = 0
        if transaction_1_state != 0:
            try:
                self.driver.get(
                    "https://ple5-radiofrance.soprahronline.sopra/app/foryou/"
                )
            except:
                self.driver.quit()
                sys.exit(0)
        end_time = time()
        if transaction_1_state != 0:
            transaction_1_time = round((end_time - start_time), 3)
            transaction_1_time = transaction_1_time + temps_attente_tr1
            # self.affichage_tr(1, transaction_1_state, transaction_1_time)
        else:
            self.driver.quit()
            sys.exit(0)
        # # 1 | open | /app/foryou/ |
        # self.driver.get(
        # 	"https://ple5-radiofrance.soprahronline.sopra/app/foryou/")

        #! transaction_2
        # Saisir les champs suivants :
        # Nom du champ	Valeur
        # Identifiant 	Identifiant Utilisateur (voir jeu de données)
        # Mot de passe 	Mot de passe Utilisateur (voir jeu de données)
        # Cliquer sur le bouton « Me connecter »
        # La page principale de 4YOU s’affiche.

        start_time = time()
        try:
            # 2 | setWindowSize | 1550x838 |
            self.driver.set_window_size(1550, 838)
            # 3 | click | id=loginInput |
            try:
                wait = WebDriverWait(self.driver, 20)
                element_loginInput = wait.until(
                    EC.element_to_be_clickable((By.ID, "loginInput")))
                element_loginInput.click()
            except:
                print("element ID=loginInput introuvable!")
                self.driver.quit()
                sys.exit(0)

            # 4 | type | id=loginInput | CDM
            try:
                element_loginInput.send_keys("CDM")
            except:
                self.driver.quit()
                sys.exit(0)
            # 5 | click | id=passwordInput |
            try:
                wait = WebDriverWait(self.driver, 20)
                element_passwordInput = wait.until(
                    EC.element_to_be_clickable((By.ID, "passwordInput"))
                )
                element_passwordInput.click()
            except:
                print("element ID=passwordInput introuvable!")
                self.driver.quit()
                sys.exit(0)
            # 6 | type | id=passwordInput | RadioFrance2022**
            try:
                element_passwordInput.send_keys("RadioFrance2022**")
            except:
                self.driver.quit()
                sys.exit(0)
            # 7 | click | id=loginButton
            try:
                wait = WebDriverWait(self.driver, 20)
                element_loginButton = wait.until(
                    EC.element_to_be_clickable((By.ID, "loginButton")))
                element_loginButton.click()
            except:
                self.driver.quit()
                sys.exit(0)
            transaction_2_state = 1
        except:
            transaction_2_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_2_state != 0:
            transaction_2_time = round((end_time - start_time), 3)
            transaction_2_time = transaction_2_time + temps_attente_tr2
            # self.affichage_tr(2, transaction_2_state, transaction_2_time)
        else:
            self.driver.quit()
            sys.exit(0)

        # # 2 | click | id=loginInput |
        # self.driver.find_element(By.ID, "loginInput").click()
        # # 3 | type | id=loginInput | CDM
        # self.driver.find_element(By.ID, "loginInput").send_keys("CDM")
        # # 4 | click | id=passwordInput |
        # self.driver.find_element(By.ID, "passwordInput").click()
        # # 5 | type | id=passwordInput | RadioFrance2022**
        # self.driver.find_element(
        #     By.ID, "passwordInput").send_keys("RadioFrance2022**")
        # # 6 | click | id=loginButton |
        # self.driver.find_element(By.ID, "loginButton").click()

        #! transaction_3
        #! Le menu d’accueil d’Espace gestionnaire s’affiche.
        start_time = time()
        try:
            transaction_3_state = 1
        except:
            transaction_3_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_3_state != 0:
            transaction_3_time = round((end_time - start_time), 3)
            transaction_3_time = transaction_3_time + temps_attente_tr3
            # self.affichage_tr(3, transaction_3_state, transaction_3_time)
        else:
            self.driver.quit()
            sys.exit(0)
        #! transaction_4
        start_time = time()
        try:            
            # 7 | click | id=burgerMenuButton |
            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                try:
                    wait = WebDriverWait(self.driver, 20)
                    element_fa_bars = wait.until(
                        EC.element_to_be_clickable((By.ID, "burgerMenuButton")))
                    element_fa_bars.click()
                    break
                except:
                    # print("not found 1")
                    retry_count += 1
                    self.driver.refresh()  # refresh the driver before retrying

            if retry_count == max_retries:
                print("Failed to find element after maximum number of retries. 1")
                self.url_down_output()
                self.driver.quit()
                sys.exit(0)            
            # 8 | click | css=.menu-legacy-root-item > a > span |
            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                try:
                    wait = WebDriverWait(self.driver, 20)
                    element_fa_bars = wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, ".menu-legacy-root-item > a > span")))
                    element_fa_bars.click()
                    break
                except:
                    # print("not found 1")
                    retry_count += 1
                    self.driver.refresh()  # refresh the driver before retrying

            if retry_count == max_retries:
                print("Failed to find element after maximum number of retries. 2")
                self.url_down_output()
                self.driver.quit()
                sys.exit(0)            
            # 9 | click | css=ul:nth-child(4) > .white:nth-child(1) > a > span |
            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                try:
                    wait = WebDriverWait(self.driver, 20)
                    element_fa_bars = wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, "ul:nth-child(4) > .white:nth-child(1) > a > span")))
                    element_fa_bars.click()
                    break
                except:
                    # print("not found 1")
                    retry_count += 1
                    self.driver.refresh()  # refresh the driver before retrying

            if retry_count == max_retries:
                print("Failed to find element after maximum number of retries. 3")
                self.url_down_output()
                self.driver.quit()
                sys.exit(0)            
            # 10 | click | css=ul:nth-child(4) > .white:nth-child(1) > ul > .white:nth-child(3) > a > span |
            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                try:
                    wait = WebDriverWait(self.driver, 20)
                    element_fa_bars = wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, "ul:nth-child(4) > .white:nth-child(1) > ul > .white:nth-child(3) > a > span")))
                    element_fa_bars.click()
                    break
                except:
                    # print("not found 1")
                    retry_count += 1
                    self.driver.refresh()  # refresh the driver before retrying

            if retry_count == max_retries:
                print("Failed to find element after maximum number of retries. 4")
                self.url_down_output()
                self.driver.quit()
                sys.exit(0)            
            # 11 | click | css=.white:nth-child(1) > ul > .white:nth-child(3) .white:nth-child(8) span |
            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                try:
                    wait = WebDriverWait(self.driver, 20)
                    element_fa_bars = wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, ".white:nth-child(1) > ul > .white:nth-child(3) .white:nth-child(8) span")))
                    element_fa_bars.click()
                    break
                except:
                    # print("not found 1")
                    retry_count += 1
                    self.driver.refresh()  # refresh the driver before retrying

            if retry_count == max_retries:
                print("Failed to find element after maximum number of retries. 5")
                self.url_down_output()
                self.driver.quit()
                sys.exit(0)            
            transaction_4_state = 1
        except:
            transaction_4_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_4_state != 0:
            transaction_4_time = round((end_time - start_time), 3)
            transaction_4_time = transaction_4_time + temps_attente_tr4
            # self.affichage_tr(4, transaction_4_state, transaction_4_time)
        else:
            self.driver.quit()
            sys.exit(0)         
        #! transaction_5
        start_time = time()
        try:               
            # 12 | selectFrame | index=0 |
            self.driver.switch_to.frame(0)
            # 13 | click | id=xrSalarie_pMatricule |
            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                try:
                    wait = WebDriverWait(self.driver, 20)
                    element_fa_bars = wait.until(
                        EC.element_to_be_clickable((By.ID, "xrSalarie_pMatricule")))
                    element_fa_bars.click()
                    break
                except:
                    # print("not found 1")
                    retry_count += 1
                    self.driver.refresh()  # refresh the driver before retrying

            if retry_count == max_retries:
                print("Failed to find element after maximum number of retries. 6")
                self.url_down_output()
                self.driver.quit()
                sys.exit(0)            
            # 14 | type | id=xrSalarie_pMatricule | 00940288
            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                try:
                    wait = WebDriverWait(self.driver, 20)
                    element_fa_bars = wait.until(
                        EC.element_to_be_clickable((By.ID, "xrSalarie_pMatricule")))
                    element_fa_bars.send_keys("00940288")
                    break
                except:
                    # print("not found 1")
                    retry_count += 1
                    self.driver.refresh()  # refresh the driver before retrying

            if retry_count == max_retries:
                print("Failed to find element after maximum number of retries. 7")
                self.url_down_output()
                self.driver.quit()
                sys.exit(0)            
            transaction_5_state = 1
        except:
            transaction_5_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_5_state != 0:
            transaction_5_time = round((end_time - start_time), 3)
            transaction_5_time = transaction_5_time + temps_attente_tr5
        else:
            self.driver.quit()
            exit(0)       
        #! transaction_6
        start_time = time()
        try:                       
            # 15 | click | id=xrSalarie_EButton_0 |
            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                try:
                    wait = WebDriverWait(self.driver, 20)
                    element_fa_bars = wait.until(
                        EC.element_to_be_clickable((By.ID, "xrSalarie_EButton_0")))
                    element_fa_bars.click()
                    break
                except:
                    # print("not found 1")
                    retry_count += 1
                    self.driver.refresh()  # refresh the driver before retrying

            if retry_count == max_retries:
                print("Failed to find element after maximum number of retries. 8")
                self.url_down_output()
                self.driver.quit()
                sys.exit(0)            
            # 16 | selectFrame | relative=parent |
            self.driver.switch_to.default_content()
            # 17 | click | xpath=/html/body/div[1]/div[3]/div/div[2]/div/div/div[1]/div/div[1]/div/div/div[1]/div/div/div[3]/select/option[3] |
            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                try:
                    wait = WebDriverWait(self.driver, 20)
                    element_fa_bars = wait.until(
                        EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div[3]/div/div[2]/div/div/div[1]/div/div[1]/div/div/div[1]/div/div/div[3]/select/option[3]")))
                    element_fa_bars.click()
                    break
                except:
                    # print("not found 1")
                    retry_count += 1
                    self.driver.refresh()  # refresh the driver before retrying

            if retry_count == max_retries:
                print("Failed to find element after maximum number of retries. 9")
                self.url_down_output()
                self.driver.quit()
                sys.exit(0)            
            # # 18 | click | css=option:nth-child(3) |
            # max_retries = 10
            # retry_count = 0

            # while retry_count < max_retries:
            #     try:
            #         wait = WebDriverWait(self.driver, 20)
            #         element_fa_bars = wait.until(
            #             EC.element_to_be_clickable((By.CSS_SELECTOR, "option:nth-child(3)")))
            #         element_fa_bars.click()
            #         break
            #     except:
            #         # print("not found 1")
            #         retry_count += 1
            #         self.driver.refresh()  # refresh the driver before retrying

            # if retry_count == max_retries:
            #     print("Failed to find element after maximum number of retries. 10")
            #     self.url_down_output()
            #     self.driver.quit()
            #     sys.exit(0)            
            # # 19 | selectFrame | index=0 |
            # self.driver.switch_to.frame(0)
            # # 20 | click | linkText=00940288 |
            # max_retries = 10
            # retry_count = 0

            # while retry_count < max_retries:
            #     try:
            #         wait = WebDriverWait(self.driver, 20)
            #         element_fa_bars = wait.until(
            #             EC.element_to_be_clickable((By.LINK_TEXT, "00940288")))
            #         element_fa_bars.click()
            #         break
            #     except:
            #         # print("not found 1")
            #         retry_count += 1
            #         self.driver.refresh()  # refresh the driver before retrying

            # if retry_count == max_retries:
            #     print("Failed to find element after maximum number of retries. 11")
            #     self.url_down_output()
            #     self.driver.quit()
            #     sys.exit(0)            
            transaction_6_state = 1
        except:
            transaction_6_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_6_state != 0:
            transaction_6_time = round((end_time - start_time), 3)
            transaction_6_time = transaction_6_time + temps_attente_tr6
        else:
            self.driver.quit()
            exit(0)            
        #! transaction_7
        start_time = time()
        try:            
            # # 21 | selectFrame | relative=parent |
            # self.driver.switch_to.default_content()
            # # 22 | click | id=navbar-logout-link-id |
            # max_retries = 10
            # retry_count = 0

            # while retry_count < max_retries:
            #     try:
            #         wait = WebDriverWait(self.driver, 20)
            #         element_fa_bars = wait.until(
            #             EC.element_to_be_clickable((By.ID, "navbar-logout-link-id")))
            #         element_fa_bars.click()
            #         break
            #     except:
            #         print("not found 1")
            #         retry_count += 1
            #         self.driver.refresh()  # refresh the driver before retrying

            # if retry_count == max_retries:
            #     print("Failed to find element after maximum number of retries. 12")
            #     self.url_down_output()
            #     self.driver.quit()
            #     sys.exit(0)            
            # # 23 | close |  |
            # self.driver.close()
            transaction_7_state = 1
        except:
            transaction_7_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_7_state != 0:
            transaction_7_time = round((end_time - start_time), 3)
            transaction_7_time = transaction_7_time + temps_attente_tr7
        else:
            self.driver.quit()
            exit(0)
        scenario = transaction_7_state
        totale = (
            transaction_1_time
            + transaction_2_time
            + transaction_3_time
            + transaction_4_time
            + transaction_5_time
            + transaction_6_time
            + transaction_7_time
            + temps_saisie
        )
        if transaction_1_state != 0 and transaction_2_state != 0 and transaction_3_state != 0 and transaction_4_state != 0 and transaction_5_state != 0 and transaction_6_state != 0 and transaction_7_state != 0:
            self.affichage_tr(1, transaction_1_state, transaction_1_time)
            self.affichage_tr(2, transaction_2_state, transaction_2_time)
            self.affichage_tr(3, transaction_3_state, transaction_3_time)
            self.affichage_tr(4, transaction_4_state, transaction_4_time)
            self.affichage_tr(5, transaction_5_state, transaction_5_time)
            self.affichage_tr(6, transaction_6_state, transaction_6_time)
            self.affichage_tr(7, transaction_7_state, transaction_7_time)
            print("scenario={:.1f}".format(scenario))
            print("scenariotime={:.2f}".format(totale))
        else:
            self.url_down_output()

    def perf_mesaure(self):
        self.setup_method(self)
        self.test_sc8()
        self.teardown_method()


t = TestSc8()
if t.check_url() == True:
    t.perf_mesaure()
    t.teardown_method()
else:
    t.url_down_output()
            
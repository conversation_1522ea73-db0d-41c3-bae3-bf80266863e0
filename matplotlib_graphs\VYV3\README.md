
1. build
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080

cd /data2/dockerized_rpa_v2/matplotlib_graphs/VYV3 
podman build -t vyv-automated-cmplx .

2. running in general:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/VYV3/pdfs:/app/output vyv-automated-cmplx

3. running for may:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/VYV3/pdfs:/app/output vyv-automated-cmplx "HR - scénario PERF VYV3" "2023-05-01" "2023-05-31" "VYV3"

4. running for august:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/VYV3/pdfs:/app/output vyv-automated-cmplx "HR - scénario PERF VYV3" "2023-08-01" "2023-08-31" "VYV3"

sh run_docker_scenarios.sh

podman run vyv-automated-cmplx


ssh admrpa@frsopslapp065 "docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/VYV3/pdfs:/app/output vyv-automated-cmplx"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/VYV3/pdfs:/app/output vyv-automated-cmplx


docker run -it vyv-automated-cmplx /bin/bash
podman exec -it  vyv-automated-cmplx /bin/bash

podman run --rm vyv-automated-cmplx



podman save -o vyv-automated-cmplx.tar vyv-automated-cmplx
scp vyv-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/vyv-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913

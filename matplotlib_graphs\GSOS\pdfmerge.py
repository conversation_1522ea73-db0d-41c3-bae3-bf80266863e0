import PyPDF2
import subprocess
import os
import sys

# Script: pdfmerge.py
# Description: pdf report generator
# Version: 2
# Changes:
# - Fixed a bug where the script executed for the current month instead of the last month.

# Get the absolute path of the current directory
current_directory = os.path.abspath(os.getcwd())
print("Current Directory:", current_directory)

# Check if there are command-line arguments
if len(sys.argv) != 5:
    # print("Usage: python3 scripts.py <argument1:client> <argument2:mois>")
    sys.exit(1)

scenario = sys.argv[1]
print("selected scenario:\n", scenario)
start_date = sys.argv[2]
print("selected start_date:\n", start_date)
end_date = sys.argv[3]
print("selected end_date:\n", end_date)
client_name = sys.argv[4]
print("selected client_name:\n", client_name)


# Run script1.py
print("running report_graphs.py")
subprocess.run(['python3', 'report_graphs.py', scenario, start_date, end_date, client_name])

# Run script2.py
print("running report_tables.py")
subprocess.run(['python3', 'report_tables.py', scenario, start_date, end_date, client_name])



# Define a function to merge PDFs
def merge_pdfs(input_pdfs, output_pdf):
    merger = PyPDF2.PdfMerger()

    for pdf in input_pdfs:
        merger.append(pdf)

    with open(output_pdf, 'wb') as output:
        merger.write(output)

    # Delete input PDF files
    for pdf in input_pdfs:
        pdf_file = open(pdf, 'rb')
        pdf_file.close()  # Close the file handle before deletion
        os.remove(pdf)

# Change working directory to /app/
os.chdir('/app/')
# List of input PDF files to merge
input_pdfs = ['/app/output/tables.pdf', '/app/output/graphs.pdf']

import datetime

# Get current date and time
current_datetime = datetime.datetime.now()

# Calculate last month's year and month
if current_datetime.month == 1:
    last_month_year = current_datetime.year - 1
    last_month = 12
else:
    last_month_year = current_datetime.year
    last_month = current_datetime.month - 1

# Add leading zero if month is less than 10
last_month_str = str(last_month).zfill(2)

# Calculate last month's start date
start_date = f"{last_month_year}-{last_month_str}-01"

# Format date and time components
year = current_datetime.year
month = current_datetime.month
day = current_datetime.day
hour = current_datetime.hour
minute = current_datetime.minute

# Construct the PDF file name with the formatted components and the argument
output_pdf = f'/app/output/rapport_{scenario}_{last_month_year}_{last_month_str}.pdf'

# Call the function to merge the PDFs and delete input PDFs
merge_pdfs(input_pdfs, output_pdf)

from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import smtplib
import subprocess
import os, re
import shutil
from email.mime.application import MIMEApplication
from datetime import datetime, timedelta

# cleanup for c in $(ll -t | egrep -i 'AXA|LCL|GSOS|TRANSDEV|VYV3|RADIOF|COVEA' | awk -F " " '{print $9}'); do rm  $c/pdfs/*; done
# for c in $(ll -t | egrep -i 'AXA|LCL|GSOS|TRANSDEV|VYV3|RADIOF|COVEA' | grep -v 'archivepdfs' | awk -F " " '{print $9}'); do rm $c/pdfs/*; done
# for c in $(ll -t | egrep -i 'AXA|LCL|GSOS|TRANSDEV|VYV3|RADIOF|COVEA' | awk -F " " '{print $9}'); do find $c/pdfs/* -type d -not -name "archivepdfs" -exec rm -r {} \;; done

# recipients = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
recipients = ["<EMAIL>"]
# bcc_recipients = ["<EMAIL>", "<EMAIL>"]
bcc_recipients = ["<EMAIL>"]
# bcc_recipients = ["<EMAIL>"]

MAIN_PATH = "/data2/dockerized_rpa_v2/matplotlib_graphs/"
def send_email(file_name, recipients, bcc_recipients, subject, body, smtp_server, smtp_port, from_user):
	msg = MIMEMultipart()
	msg['From'] = from_user
	msg['To'] = ', '.join(recipients)
	msg['Subject'] = subject
	msg.attach(MIMEText(body, 'html'))

	with open(file_name, "rb") as attachment:
		part = MIMEApplication(attachment.read(), _subtype="pdf")
		part.add_header('Content-Disposition', 'attachment', filename=os.path.basename(file_name))
		msg.attach(part)

	server = smtplib.SMTP(smtp_server, smtp_port)
	server.sendmail(from_user, recipients + bcc_recipients, msg.as_string())
	server.quit()

def generate_email(pdf_filename, CLIENTNAME):
	# Paramètres du serveur SMTP
	smtp_server = "smtp.soprahronline.sopra"
	smtp_port = 25
	from_user = "<EMAIL>"
	current_date = datetime.now()
	previous_month = current_date.replace(day=1) - timedelta(days=1)
	year_month = previous_month.strftime("%Y%m")
	subject = f"[{CLIENTNAME}][{year_month}] Rapport de performance web"
			
	content = u"""\
	Bonjour,
	<p>Voici le rapport de performance du client : <strong>%s</strong> sur la période du <strong>%s</strong>.</p>
	<p>Le fichier contient des informations détaillées sur les performances des différentes urls (accessiblité / disponibilité) et les downtimes.</p>
	<p>Si vous avez des questions ou besoin de plus d'informations, n'hésitez pas à nous contacter.</p>
	<p>Cordialement,</p>
	<p>L'équipe Prodops</p>
	""" % (CLIENTNAME, year_month)

	titre = CLIENTNAME

	body = u"""\
	<!DOCTYPE html PUBLIC '-//W3C//DTD XHTML 1.0 Transitional//EN' 'http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd'>
	<html>
	<head>
			<meta name='viewport' content='width=device-width, initial-scale=1.0'>
			<meta http-equiv='Content-Type' content='text/html; charset=utf-8' />
			<title>Reporting Prodops</title>
			<style type='text/css'>
			table {border-collapse: collapse;}
			ul {margin:0;}
			</style>
	</head>

	<body marginheight='0' marginwidth='0' topmargin='0' leftmargin='0' bgcolor='#f2f2f2'>

	<table width='100%%' height='100%%' border='0' cellspacing='0' cellpadding='0' bgcolor='#f2f2f2'>
	<tr>
			<td valign='top' align='middle'>
			<table width='80%%' border='0' cellspacing='0' cellpadding='0'>
					<tr>
					<td align='left' bgcolor='#ffffff' style='BORDER-RIGHT: #9c9c9c 1px solid; PADDING-RIGHT: 5px; BORDER-TOP: #9c9c9c 1px solid; PADDING-LEFT: 5px; PADDING-BOTTOM: 5px; BORDER-LEFT: #9c9c9c 1px solid; PADDING-TOP: 5px; BORDER-BOTTOM: #9c9c9c 1px solid'>
							<table width='100%%' border='0' cellspacing='0' cellpadding='0' style='BORDER-RIGHT: #c7001b 7px solid; BORDER-TOP: #c7001b 7px solid; BORDER-LEFT: #c7001b 7px solid; BORDER-BOTTOM: #c7001b 7px solid'>
									<tr>
									<td style='PADDING-RIGHT: 20px; PADDING-LEFT: 20px; PADDING-BOTTOM: 10px; PADDING-TOP: 10px'>
											<table width='100%%' border='0' cellspacing='0' cellpadding='0'>
											<tr>
													<td><img src='https://jira.soprahronline.com/s/fr_FR-3azjpq/649/23/_/jira-logo-scaled.png' alt='' ></td>
													<td align='right' valign='center' style='FONT-SIZE: 26px; COLOR: #333333; FONT-FAMILY: Arial, Helvetica, sans-serif;'>Reporting %s</td>
											</tr>
											</table>
									</td>
									</tr>
									<tr style='BACKGROUND-COLOR: #c7001b; height:39px;'>
									<td>
											
									</td>
									</tr>
									<tr>
									<td style='PADDING-RIGHT: 20px; PADDING-LEFT: 20px; FONT-SIZE: 14px; PADDING-BOTTOM: 0px; COLOR: #333333; LINE-HEIGHT: 20px; PADDING-TOP: 30px; FONT-FAMILY: Arial, Helvetica, sans-serif; '>
									%s
									<tr>
											<td style='PADDING-RIGHT: 0px;  BACKGROUND-COLOR: #ffffff'>
											&nbsp;
											</td>
									</tr>
									<tr>
											<td style='PADDING-RIGHT: 0px; BORDER-TOP: #d6d6d6 1px solid; PADDING-LEFT: 20px; FONT-SIZE: 12px;  PADDING-BOTTOM: 15px; COLOR: #666666; LINE-HEIGHT: 17px; PADDING-TOP: 15px; FONT-FAMILY: Arial, Helvetica, sans-serif; BACKGROUND-COLOR: #f6f6f6'>
											&nbsp;En cas de probl&egrave;mes constat&eacute;s, vous pouvez ouvrir un ticket <a target='_blank' href='https://jira.soprahronline.com/login.jsp' style='COLOR: #4089bb'>JIRA</a>
											</td>
									</tr>
							</table>
					</td>
					</tr>
			</table>
			</td>
	</tr>
	</table>
	</body>
	</html>     
	""" % (titre, content)

	# corps = u"""test"""

	# body=body.encode('utf-8','ignore')
	# print('corps:%s'%(corps))


	send_email(pdf_filename, recipients, bcc_recipients, subject, body, smtp_server, smtp_port, from_user)

	print("Le rapport de performance web a été envoyé avec succès.")


def copy_pdfs_to_archive():
	source_directories = [
		# MAIN_PATH+"AXA/pdfs/",
		# MAIN_PATH+"LCL/pdfs/",
		# MAIN_PATH+"GSOS/pdfs/",
		# MAIN_PATH+"TRANSDEV/pdfs/",
		# MAIN_PATH+"VYV3/pdfs/",
		# MAIN_PATH+"COVEA/pdfs/",
		# MAIN_PATH+"RADIOF/pdfs/",
		MAIN_PATH+"BGM/pdfs/",
	]
	current_date = datetime.now()
	previous_month = current_date.replace(day=1) - timedelta(days=1)
	year_month = previous_month.strftime("%Y%m")
	# Retrieve day, hour, and minute
	day = current_date.day
	hour = current_date.hour
	minute = current_date.minute
	for source_dir in source_directories:
		# Create archivepdfs directory within the source directory
		archive_dir = os.path.join(source_dir, 'archivepdfs')
		os.makedirs(archive_dir, exist_ok=True)
		
		# Extract client name using regular expression
		match = re.search(r'[^/]+(?=/pdfs/)', source_dir)
		client_name = match.group() if match else "Unknown"
		print(f"We're now processing {client_name} client.")

		# Copy PDF files from source to archive directory
		for pdf_file in os.listdir(source_dir):
			if pdf_file.endswith('.pdf'):
				shutil.copy(os.path.join(source_dir, pdf_file), archive_dir)

		# Zip PDF files inside the source directory

		# Zip PDF files inside the source directory
		pdf_files = [file for file in os.listdir(archive_dir) if file.endswith('.pdf')]
		if pdf_files:
			zip_file_name = f"reports_of_{client_name}_{year_month}.zip"
			zip_file_path = os.path.join(MAIN_PATH, client_name, "pdfs", zip_file_name)
			print(zip_file_path)
			shutil.make_archive(os.path.join(MAIN_PATH, client_name, "pdfs", f"reports_of_{client_name}_{year_month}"), 'zip', archive_dir)
			print(f"Zip file created: {zip_file_name}")
			generate_email(zip_file_path, client_name)


# # Call the function to copy PDFs to the archive directory
# copy_pdfs_to_archive()


def pdf_generators():
	pdfs_script_generators = [
		# MAIN_PATH+"AXA/run_docker_scenarios_dev.sh",
		# MAIN_PATH+"LCL/run_docker_scenarios.sh",
		# MAIN_PATH+"GSOS/run_docker_scenarios.sh",
		# MAIN_PATH+"TRANSDEV/run_docker_scenarios.sh",
		# MAIN_PATH+"VYV3/run_docker_scenarios_dev.sh",
		# MAIN_PATH+"COVEA/run_docker_scenarios.sh",
		# MAIN_PATH+"RADIOF/run_docker_scenarios_dev.sh",
		MAIN_PATH+"BGM/run_docker_scenarios_dev.sh",
	]
	for script_generator in pdfs_script_generators:
		subprocess.run([script_generator], shell=True)
	copy_pdfs_to_archive()

# deactivate temporary
#^activated 
pdf_generators()





# generate_email("/data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs/\"rapport_PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR_2024_03.pdf\"")
# generate_email("/data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs/rapport_PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR_2024_03.pdf")
# generate_email("/data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs/rapport_PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR_2024_03.pdf", "AXA")

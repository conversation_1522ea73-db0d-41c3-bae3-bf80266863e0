Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(32, 42)]
temps_reponse_nominal:
 42
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(63, 79)]
temps_reponse_nominal:
 79
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 3 : GTA'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 3 : GTA'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(69, 89)]
temps_reponse_nominal:
 89
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 3 : GTA'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 3 : GTA'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 3 : GTA' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 3 : GTA
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(63, 79)]
temps_reponse_nominal:
 79
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 5 : Portail de services'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 5 : Portail de services'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(38, 50)]
temps_reponse_nominal:
 50
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 5 : Portail de services'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 5 : Portail de services'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 5 : Portail de services' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 5 : Portail de services
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(36, 50)]
temps_reponse_nominal:
 50
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(62, 50)]
temps_reponse_nominal:
 50
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(62, 78)]
temps_reponse_nominal:
 78
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'HR - scénario PERF LCL'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%LCL%'
                AND cc1.CUSTCONFLABEL LIKE 'LCL-PROD'
                AND ksn.name like 'HR - scénario PERF LCL'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%hra-space/?nosso%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%LCL%'
                AND cc2.CUSTCONFLABEL LIKE 'LCL-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%hra-space/?nosso%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(-1, -1)]
temps_reponse_nominal:
 -1
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'HR - scénario PERF LCL'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'HR - scénario PERF LCL'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'HR - scénario PERF LCL' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 HR - scénario PERF LCL
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 LCL
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'HR - scénario PERF GSOS'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME
                FROM
                    khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON
                    kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON
                    ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON
                    cpf.CUSTCONFWEBID = ccw1.ID
                JOIN clients_conf cc1 ON
                    ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%GROUPE SOS%'
                    AND cc1.CUSTCONFLABEL LIKE 'GROUPE SOS-PROD'
                    AND ksn.name like 'HR - scénario PERF GSOS'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%hra-space/portal%'
                JOIN cust_config_web ccw2 ON
                    cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON
                    ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%GROUPE SOS%'
                    AND cc2.CUSTCONFLABEL LIKE 'GROUPE SOS-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%hra-space/portal%'
                GROUP BY
                    cpf.NOMINALTIME,
                    cpf.SCENARIOID
records:
 [(-1, -1)]
temps_reponse_nominal:
 -1
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'HR - scénario PERF GSOS'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'HR - scénario PERF GSOS'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'HR - scénario PERF GSOS' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 HR - scénario PERF GSOS
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 GSOS
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'Scenario TRANSDEV 1: Profile Gestionnaire de paie'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%TRANSDEV%'
                AND cc1.CUSTCONFLABEL LIKE 'TRANSDEV-PROD'
                AND ksn.name like 'PL - scénario PERF TRANSDEV'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades2/portal/index.jsp%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%TRANSDEV%'
                AND cc2.CUSTCONFLABEL LIKE 'TRANSDEV-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades2/portal/index.jsp%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 []
Traceback (most recent call last):
  File "report_tables.py", line 41, in <module>
    ["Temps de réponse nominale:", temps_nominale(
  File "/app/get_data.py", line 423, in temps_nominale
    temps_reponse_nominal = records[-1][-1]
IndexError: list index out of range
Current Directory: /app
selected scenario:
 Scenario TRANSDEV 1: Profile Gestionnaire de paie
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 TRANSDEV
running report_graphs.py
running report_tables.py
Traceback (most recent call last):
  File "pdfmerge.py", line 92, in <module>
    merge_pdfs(input_pdfs, output_pdf)
  File "pdfmerge.py", line 46, in merge_pdfs
    merger.append(pdf)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_utils.py", line 417, in wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_merger.py", line 319, in append
    self.merge(len(self.pages), fileobj, outline_item, pages, import_outline)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_utils.py", line 417, in wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_merger.py", line 194, in merge
    stream, encryption_obj = self._create_stream(fileobj)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_merger.py", line 263, in _create_stream
    stream = FileIO(fileobj, "rb")
FileNotFoundError: [Errno 2] No such file or directory: '/app/output/tables.pdf'
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'Scenario TRANSDEV 2: Profile DRH'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%TRANSDEV%'
                AND cc1.CUSTCONFLABEL LIKE 'TRANSDEV-PROD'
                AND ksn.name like 'PL - scénario PERF TRANSDEV'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades2/portal/index.jsp%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%TRANSDEV%'
                AND cc2.CUSTCONFLABEL LIKE 'TRANSDEV-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades2/portal/index.jsp%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 []
Traceback (most recent call last):
  File "report_tables.py", line 41, in <module>
    ["Temps de réponse nominale:", temps_nominale(
  File "/app/get_data.py", line 423, in temps_nominale
    temps_reponse_nominal = records[-1][-1]
IndexError: list index out of range
Current Directory: /app
selected scenario:
 Scenario TRANSDEV 2: Profile DRH
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 TRANSDEV
running report_graphs.py
running report_tables.py
Traceback (most recent call last):
  File "pdfmerge.py", line 92, in <module>
    merge_pdfs(input_pdfs, output_pdf)
  File "pdfmerge.py", line 46, in merge_pdfs
    merger.append(pdf)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_utils.py", line 417, in wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_merger.py", line 319, in append
    self.merge(len(self.pages), fileobj, outline_item, pages, import_outline)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_utils.py", line 417, in wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_merger.py", line 194, in merge
    stream, encryption_obj = self._create_stream(fileobj)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_merger.py", line 263, in _create_stream
    stream = FileIO(fileobj, "rb")
FileNotFoundError: [Errno 2] No such file or directory: '/app/output/tables.pdf'
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'HR - scénario PERF VYV3'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%VYV3%'
                AND cc1.CUSTCONFLABEL LIKE 'VYV3-PROD'
                AND ksn.name like 'HR - scénario PERF VYV3'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%hra-space/portal%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%VYV3%'
                AND cc2.CUSTCONFLABEL LIKE 'VYV3-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%hra-space/portal%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(-1, -1)]
temps_reponse_nominal:
 -1
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'HR - scénario PERF VYV3'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'HR - scénario PERF VYV3'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'HR - scénario PERF VYV3' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 HR - scénario PERF VYV3
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 VYV3
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%COVEA%'
                    AND cc1.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ksn.name like 'PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
                    AND ksn.name like 'PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%COVEA%'
                    AND cc2.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(38, 55)]
temps_reponse_nominal:
 55
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 COVEA
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%COVEA%'
                    AND cc1.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ksn.name like 'PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
                    AND ksn.name like 'PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%COVEA%'
                    AND cc2.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(42, 61)]
temps_reponse_nominal:
 61
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 COVEA
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - COVEA - Scénario 3 : Liste des métiers, domaines, contextes liés à un contrat'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%COVEA%'
                    AND cc1.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ksn.name like 'PL - COVEA - Scénario 3 : Liste des métiers, domaines, contextes liés à un contrat'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
                    AND ksn.name like 'PL - COVEA - Scénario 3 : Liste des métiers, domaines, contextes liés à un contrat'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%COVEA%'
                    AND cc2.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(40, 56)]
temps_reponse_nominal:
 56
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 3 : Liste des métiers, domaines, contextes liés à un contrat'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 3 : Liste des métiers, domaines, contextes liés à un contrat'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - COVEA - Scénario 3 : Liste des métiers, domaines, contextes liés à un contrat' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - COVEA - Scénario 3 : Liste des métiers, domaines, contextes liés à un contrat
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 COVEA
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - COVEA - Scénario 4 : Liste des horaires GTA'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%COVEA%'
                    AND cc1.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ksn.name like 'PL - COVEA - Scénario 4 : Liste des horaires GTA'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
                    AND ksn.name like 'PL - COVEA - Scénario 4 : Liste des horaires GTA'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%COVEA%'
                    AND cc2.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(34, 48)]
temps_reponse_nominal:
 48
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 4 : Liste des horaires GTA'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 4 : Liste des horaires GTA'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - COVEA - Scénario 4 : Liste des horaires GTA' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - COVEA - Scénario 4 : Liste des horaires GTA
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 COVEA
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - COVEA - Scénario 5 : Ma liste des soldes congés (Profil collaborateur Mes absences)'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%COVEA%'
                    AND cc1.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ksn.name like 'PL - COVEA - Scénario 5 : Ma liste des soldes congés (Profil collaborateur Mes absences)'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
                    AND ksn.name like 'PL - COVEA - Scénario 5 : Ma liste des soldes congés (Profil collaborateur Mes absences)'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%COVEA%'
                    AND cc2.CUSTCONFLABEL LIKE 'COVEA-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/#/login%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(24, 34)]
temps_reponse_nominal:
 34
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 5 : Ma liste des soldes congés (Profil collaborateur Mes absences)'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - COVEA - Scénario 5 : Ma liste des soldes congés (Profil collaborateur Mes absences)'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - COVEA - Scénario 5 : Ma liste des soldes congés (Profil collaborateur Mes absences)' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - COVEA - Scénario 5 : Ma liste des soldes congés (Profil collaborateur Mes absences)
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 COVEA
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 1 : Mise en paiement collective des cachetiers et pigistes'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 1 : Mise en paiement collective des cachetiers et pigistes'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(24, 48)]
temps_reponse_nominal:
 24
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 1 : Mise en paiement collective des cachetiers et pigistes%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 1 : Mise en paiement collective des cachetiers et pigistes%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 1 : Mise en paiement collective des cachetiers et pigistes%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 1 : Mise en paiement collective des cachetiers et pigistes
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 2 : Coordonnées'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 2 : Coordonnées'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(37, 74)]
temps_reponse_nominal:
 37
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 2 : Coordonnées%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 2 : Coordonnées%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 2 : Coordonnées%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 2 : Coordonnées
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 3 : Gestion des demandes'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 3 : Gestion des demandes'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(26, 52)]
temps_reponse_nominal:
 26
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 3 : Gestion des demandes%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 3 : Gestion des demandes%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 3 : Gestion des demandes%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 3 : Gestion des demandes
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 4 : Affectations'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 4 : Affectations'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(37, 74)]
temps_reponse_nominal:
 37
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 4 : Affectations%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 4 : Affectations%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 4 : Affectations%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 4 : Affectations
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 5 : Temps de travail'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 5 : Temps de travail'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(36, 72)]
temps_reponse_nominal:
 36
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 5 : Temps de travail%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 5 : Temps de travail%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 5 : Temps de travail%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 5 : Temps de travail
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 6 : Transport'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 6 : Transport'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(36, 72)]
temps_reponse_nominal:
 36
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 6 : Transport%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 6 : Transport%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 6 : Transport%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 6 : Transport
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 7 : Primes'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 7 : Primes'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(36, 72)]
temps_reponse_nominal:
 36
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 7 : Primes%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 7 : Primes%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 7 : Primes%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 7 : Primes
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 8 : Données bancaires'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 8 : Données bancaires'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(24, 48)]
temps_reponse_nominal:
 24
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 8 : Données bancaires%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 8 : Données bancaires%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 8 : Données bancaires%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 8 : Données bancaires
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 9 : Rémunération'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 9 : Rémunération'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(38, 76)]
temps_reponse_nominal:
 38
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 9 : Rémunération%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 9 : Rémunération%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 9 : Rémunération%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 9 : Rémunération
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario 10 : Fiche de synthèse'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario 10 : Fiche de synthèse'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(20, 40)]
temps_reponse_nominal:
 20
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 10 : Fiche de synthèse%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 10 : Fiche de synthèse%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario 10 : Fiche de synthèse%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario 10 : Fiche de synthèse
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario nocturne'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario nocturne'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(-1, -1)]
temps_reponse_nominal:
 -1
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario nocturne%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario nocturne%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario nocturne%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario nocturne
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - RADIOFRANCE - Scénario diurne'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%RADIOF%'
                    AND cc1.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
                    AND ksn.name like 'PL - RADIOFRANCE - Scénario diurne'
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%RADIOF%'
                    AND cc2.CUSTCONFLABEL LIKE 'RADIOF-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
records:
 [(-1, -1)]
temps_reponse_nominal:
 -1
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario diurne%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario diurne%'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE '%PL - RADIOFRANCE - Scénario diurne%' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - RADIOFRANCE - Scénario diurne
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 RADIOF
running report_graphs.py
running report_tables.py
/data2/dockerized_rpa_v2/matplotlib_graphs/BGM/run_docker_scenarios_dev.sh: line 19: 08: value too great for base (error token is "08")
/data2/dockerized_rpa_v2/matplotlib_graphs/BGM/run_docker_scenarios_dev.sh: line 22: [: : integer expression expected
date: invalid date ‘2024--01 +1 month’
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
Traceback (most recent call last):
  File "report_graphs.py", line 33, in <module>
    weekly_data = weekly_graph_data(scenario, start_date, end_date)
  File "/app/get_data.py", line 116, in weekly_graph_data
    records = cursor.fetchall()
  File "/usr/local/lib/python3.8/site-packages/mysql/connector/cursor_cext.py", line 651, in fetchall
    self._handle_eof()
  File "/usr/local/lib/python3.8/site-packages/mysql/connector/cursor_cext.py", line 236, in _handle_eof
    self._handle_warnings()
  File "/usr/local/lib/python3.8/site-packages/mysql/connector/cursor_cext.py", line 210, in _handle_warnings
    raise err
mysql.connector.errors.DatabaseError: 1292: Truncated incorrect datetime value: '2024--01'
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%GALERIE%'
                AND cc1.CUSTCONFLABEL LIKE 'GALERIE-PROD'
                AND ksn.name like 'PL - scenario PERF BGM-GALERIEE5'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%GALERIE%'
                AND cc2.CUSTCONFLABEL LIKE 'GALERIE-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiades/portal/index.jsp%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(-1, -1)]
temps_reponse_nominal:
 -1
Traceback (most recent call last):
  File "report_tables.py", line 51, in <module>
    str(month_av_perf_percentage(scenario, start_date, end_date)[0])+"%",
  File "/app/get_data.py", line 218, in month_av_perf_percentage
    records = cursor.fetchall()
  File "/usr/local/lib/python3.8/site-packages/mysql/connector/cursor_cext.py", line 651, in fetchall
    self._handle_eof()
  File "/usr/local/lib/python3.8/site-packages/mysql/connector/cursor_cext.py", line 236, in _handle_eof
    self._handle_warnings()
  File "/usr/local/lib/python3.8/site-packages/mysql/connector/cursor_cext.py", line 210, in _handle_warnings
    raise err
mysql.connector.errors.DatabaseError: 1292: Truncated incorrect datetime value: '2024--01'
Current Directory: /app
selected scenario:
 PL - scenario PERF BGM-GALERIEE5
selected start_date:
 2024--01
selected end_date:
 2024-08-05
selected client_name:
 BGM
running report_graphs.py
running report_tables.py
Traceback (most recent call last):
  File "pdfmerge.py", line 92, in <module>
    merge_pdfs(input_pdfs, output_pdf)
  File "pdfmerge.py", line 46, in merge_pdfs
    merger.append(pdf)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_utils.py", line 417, in wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_merger.py", line 319, in append
    self.merge(len(self.pages), fileobj, outline_item, pages, import_outline)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_utils.py", line 417, in wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_merger.py", line 194, in merge
    stream, encryption_obj = self._create_stream(fileobj)
  File "/usr/local/lib/python3.8/site-packages/PyPDF2/_merger.py", line 263, in _create_stream
    stream = FileIO(fileobj, "rb")
FileNotFoundError: [Errno 2] No such file or directory: '/app/output/tables.pdf'
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - scenario PERF CONFORAMA-CONFOE5'   
                AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                    JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
                    JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
                    JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
                    JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%CONFOE5%'
                    AND cc1.CUSTCONFLABEL LIKE 'CONFOE5-PROD'
                    AND ksn.name like 'PL - scenario PERF CONFORAMA-CONFOE5'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%pleiadesClassic/portal/index.jsp%'
                    JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID
                    JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%CONFOE5%'
                    AND cc2.CUSTCONFLABEL LIKE 'CONFOE5-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%pleiadesClassic/portal/index.jsp%'
            GROUP BY cpf.NOMINALTIME,
                    cpf.SCENARIOID
records:
 [(-1, -1)]
temps_reponse_nominal:
 -1
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - scenario PERF CONFORAMA-CONFOE5'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - scenario PERF CONFORAMA-CONFOE5'   
                    AND kdc.date_step BETWEEN '2024-07-01' AND '2024-07-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_temps_moyen_percentage Query: SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE 'PL - scenario PERF CONFORAMA-CONFOE5' 
                        AND kdc.date_step BETWEEN '2024-07-01' AND  '2024-07-31'
                        GROUP BY date_step
                    ) AS subquery
Current Directory: /app
selected scenario:
 PL - scenario PERF CONFORAMA-CONFOE5
selected start_date:
 2024-07-01
selected end_date:
 2024-07-31
selected client_name:
 CONFOE5
running report_graphs.py
running report_tables.py
Le rapport de performance web a été envoyé avec succès.
Le rapport de performance web a été envoyé avec succès.
Le rapport de performance web a été envoyé avec succès.
Le rapport de performance web a été envoyé avec succès.
Le rapport de performance web a été envoyé avec succès.
Le rapport de performance web a été envoyé avec succès.
Le rapport de performance web a été envoyé avec succès.
Le rapport de performance web a été envoyé avec succès.

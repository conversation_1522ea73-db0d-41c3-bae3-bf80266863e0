1695129312661	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "-marionette" "--headless" "-foreground" "-no-remote" "-profile" "/tmp/rust_mozprofile.9IMxAGlPaHxW"
*** You are running in headless mode.
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.951) [GFX1-]: glxtest: Unable to open a connection to the X server
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.951) |[1][GFX1-]: glxtest: libEGL initialize failed (t=1.9511) [GFX1-]: glxtest: libEGL initialize failed
1695129314640	Marionette	INFO	Marionette enabled
1695129314647	Marionette	INFO	Listening on port 41939
1695129315036	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.error: Region.jsm: "Error fetching region" (new TypeError("NetworkError when attempting to fetch resource.", ""))
console.error: Region.jsm: "Failed to fetch region" (new Error("NO_RESULT", "resource://gre/modules/Region.jsm", 419))
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.951) |[1][GFX1-]: glxtest: libEGL initialize failed (t=1.9511) |[2][GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt (t=3.58978) [GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile.9IMxAGlPaHxW/search.json.mozlz4", (void 0)))
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
1695129317765	Marionette	WARN	Ignoring event 'pageshow' because document has an invalid readyState of 'uninitialized'.
1695129318066	Marionette	INFO	Stopped listening on port 41939
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
console.error: "Error during quit-application-granted: [Exception... \"File error: Not found\"  nsresult: \"0x80520012 (NS_ERROR_FILE_NOT_FOUND)\"  location: \"JS frame :: resource:///modules/BrowserGlue.jsm :: _onQuitApplicationGranted/tasks< :: line 1996\"  data: no]"
1695129343468	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "-marionette" "--headless" "-foreground" "-no-remote" "-profile" "/tmp/rust_mozprofile.FgJU8A8dhpUI"
*** You are running in headless mode.
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=2.03623) [GFX1-]: glxtest: Unable to open a connection to the X server
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=2.03623) |[1][GFX1-]: glxtest: libEGL initialize failed (t=2.03631) [GFX1-]: glxtest: libEGL initialize failed
1695129345549	Marionette	INFO	Marionette enabled
1695129345560	Marionette	INFO	Listening on port 34884
1695129346231	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.error: Region.jsm: "Error fetching region" (new TypeError("NetworkError when attempting to fetch resource.", ""))
console.error: Region.jsm: "Failed to fetch region" (new Error("NO_RESULT", "resource://gre/modules/Region.jsm", 419))
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=2.03623) |[1][GFX1-]: glxtest: libEGL initialize failed (t=2.03631) |[2][GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt (t=3.90432) [GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile.FgJU8A8dhpUI/search.json.mozlz4", (void 0)))
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
1695129348882	Marionette	WARN	Ignoring event 'pageshow' because document has an invalid readyState of 'uninitialized'.
1695129349195	Marionette	INFO	Stopped listening on port 34884
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
console.error: "Error during quit-application-granted: [Exception... \"File error: Not found\"  nsresult: \"0x80520012 (NS_ERROR_FILE_NOT_FOUND)\"  location: \"JS frame :: resource:///modules/BrowserGlue.jsm :: _onQuitApplicationGranted/tasks< :: line 1996\"  data: no]"
1695129369558	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "-marionette" "--headless" "-foreground" "-no-remote" "-profile" "/tmp/rust_mozprofile.SsgZymgDM0rw"
*** You are running in headless mode.
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.59831) [GFX1-]: glxtest: Unable to open a connection to the X server
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.59831) |[1][GFX1-]: glxtest: libEGL initialize failed (t=1.59841) [GFX1-]: glxtest: libEGL initialize failed
1695129371180	Marionette	INFO	Marionette enabled
1695129371187	Marionette	INFO	Listening on port 33481
1695129371595	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.59831) |[1][GFX1-]: glxtest: libEGL initialize failed (t=1.59841) |[2][GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt (t=2.82999) [GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile.SsgZymgDM0rw/search.json.mozlz4", (void 0)))
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
console.warn: LoginRecipes: "getRecipes: falling back to a synchronous message for:" "https://hsm.soprahronline.sopra"
console.warn: LoginRecipes: "getRecipes: falling back to a synchronous message for:" "https://hsm.soprahronline.sopra"
JavaScript error: resource://gre/modules/LoginManagerParent.jsm, line 136: TypeError: gRecipeManager is null
console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 767))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1695129499314	Marionette	INFO	Stopped listening on port 33481
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
console.error: "Error during quit-application-granted: [Exception... \"File error: Not found\"  nsresult: \"0x80520012 (NS_ERROR_FILE_NOT_FOUND)\"  location: \"JS frame :: resource:///modules/BrowserGlue.jsm :: _onQuitApplicationGranted/tasks< :: line 1996\"  data: no]"
1695131213004	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "-marionette" "--headless" "-foreground" "-no-remote" "-profile" "/tmp/rust_mozprofile.55VUwgOYWIA1"
*** You are running in headless mode.
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.58978) [GFX1-]: glxtest: Unable to open a connection to the X server
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.58978) |[1][GFX1-]: glxtest: libEGL initialize failed (t=1.58987) [GFX1-]: glxtest: libEGL initialize failed
1695131214627	Marionette	INFO	Marionette enabled
1695131214633	Marionette	INFO	Listening on port 36293
1695131215019	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.58978) |[1][GFX1-]: glxtest: libEGL initialize failed (t=1.58987) |[2][GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt (t=2.84263) [GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile.55VUwgOYWIA1/search.json.mozlz4", (void 0)))
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
console.warn: LoginRecipes: "getRecipes: falling back to a synchronous message for:" "https://hsm.soprahronline.sopra"
console.warn: LoginRecipes: "getRecipes: falling back to a synchronous message for:" "https://hsm.soprahronline.sopra"
JavaScript error: resource://gre/modules/LoginManagerParent.jsm, line 136: TypeError: gRecipeManager is null
console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 767))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1695131342704	Marionette	INFO	Stopped listening on port 36293
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
console.error: "Error during quit-application-granted: [Exception... \"File error: Not found\"  nsresult: \"0x80520012 (NS_ERROR_FILE_NOT_FOUND)\"  location: \"JS frame :: resource:///modules/BrowserGlue.jsm :: _onQuitApplicationGranted/tasks< :: line 1996\"  data: no]"
1695131352406	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "-marionette" "--headless" "-foreground" "-no-remote" "-profile" "/tmp/rust_mozprofile.FMznLPsvICVu"
*** You are running in headless mode.
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.51152) [GFX1-]: glxtest: Unable to open a connection to the X server
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.51152) |[1][GFX1-]: glxtest: libEGL initialize failed (t=1.51162) [GFX1-]: glxtest: libEGL initialize failed
1695131353951	Marionette	INFO	Marionette enabled
1695131353958	Marionette	INFO	Listening on port 44897
1695131354347	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: glxtest: Unable to open a connection to the X server (t=1.51152) |[1][GFX1-]: glxtest: libEGL initialize failed (t=1.51162) |[2][GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt (t=2.72818) [GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile.FMznLPsvICVu/search.json.mozlz4", (void 0)))
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
console.warn: LoginRecipes: "getRecipes: falling back to a synchronous message for:" "https://hsm.soprahronline.sopra"
console.warn: LoginRecipes: "getRecipes: falling back to a synchronous message for:" "https://hsm.soprahronline.sopra"
JavaScript error: resource://gre/modules/LoginManagerParent.jsm, line 136: TypeError: gRecipeManager is null
1695131358849	Marionette	INFO	Stopped listening on port 44897
Missing chrome or resource URL: resource://gre/modules/UpdateListener.jsm
Missing chrome or resource URL: resource://gre/modules/UpdateListener.sys.mjs
console.error: "Error during quit-application-granted: [Exception... \"File error: Not found\"  nsresult: \"0x80520012 (NS_ERROR_FILE_NOT_FOUND)\"  location: \"JS frame :: resource:///modules/BrowserGlue.jsm :: _onQuitApplicationGranted/tasks< :: line 1996\"  data: no]"
console.error: Region.jsm: "Error fetching region" (new TypeError("NetworkError when attempting to fetch resource.", ""))
console.error: Region.jsm: "Failed to fetch region" (new Error("NO_RESULT", "resource://gre/modules/Region.jsm", 419))

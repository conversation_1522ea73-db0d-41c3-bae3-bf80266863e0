#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
    output_failure_results,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["areas"]["username"], credentials["areas"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-areas.soprahronline.sopra/pleiadesClassic/portal/index.jsp"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Load credentials
try:
    username, password = load_credentials()
except Exception as e:
    logging.error(f"Failed to load credentials: {e}")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    #! transaction_1: Navigation and page setup
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to AREAS login page",
        measure_time=True,
        unique_str="step1",
        total_steps=1,
    )

    #! transaction_2: Enter username
    check_and_log(
        test.driver,
        lambda: test.enter_text_by_id("Utilisateur", username),
        "Step 2: Enter username",
        measure_time=True,
        unique_str="step2",
        total_steps=1,
    )

    #! transaction_3: Enter password
    check_and_log(
        test.driver,
        lambda: test.enter_text_by_name("password", password),
        "Step 3: Enter password",
        measure_time=True,
        unique_str="step3",
        total_steps=1,
    )

    # Step 4: Take screenshot before login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 4: Screenshot before login"),
        "Step 4: Take screenshot before login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_4: Login and verify success
    check_and_log(
        test.driver,
        lambda: test.click_and_verify_login("btnLogin", "#Btn_Welcome_Exit > use"),
        "Step 5: Click login and verify logout icon appears",
        measure_time=True,
        unique_str="step5",
        total_steps=1,
    )

    # Step 6: Take screenshot after successful login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 6: Screenshot after successful login"),
        "Step 6: Take screenshot after successful login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        is_last_step=True,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")
    # Output failure results to stdout
    output_failure_results()

finally:
    # Cleanup
    test.teardown_driver()

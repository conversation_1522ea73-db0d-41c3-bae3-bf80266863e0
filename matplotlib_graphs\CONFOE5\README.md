
1. build
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080

cd /data2/dockerized_rpa_v2/matplotlib_graphs/CONFOE5

podman build -t confoe5-automated-cmplx .

2. running in general:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/CONFOE5/pdfs:/app/output confoe5-automated-cmplx

3. running for may:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/CONFOE5/pdfs:/app/output confoe5-automated-cmplx "HR - scénario PERF CONFOE5" "2023-05-01" "2023-05-31" "CONFOE5"

4. running for august:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/CONFOE5/pdfs:/app/output confoe5-automated-cmplx "HR - scénario PERF CONFOE5" "2023-10-01" "2023-10-31" "CONFOE5"

sh run_docker_scenarios.sh

podman run confoe5-automated-cmplx


ssh admrpa@frsopslapp065 "docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/CONFOE5/pdfs:/app/output confoe5-automated-cmplx"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/CONFOE5/pdfs:/app/output confoe5-automated-cmplx


docker run -it confoe5-automated-cmplx /bin/bash
podman exec -it  confoe5-automated-cmplx /bin/bash

podman run --rm confoe5-automated-cmplx



podman save -o confoe5-automated-cmplx.tar confoe5-automated-cmplx
scp confoe5-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/confoe5-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913


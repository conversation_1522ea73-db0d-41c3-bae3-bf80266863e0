
1. build
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080
cd /data2/dockerized_rpa_v2/matplotlib_graphs/TRANSDEV/

podman build -t transdev-automated-cmplx .

2. running in general:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/TRANSDEV/pdfs:/app/output transdev-automated-cmplx

3. running for may:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/TRANSDEV/pdfs:/app/output transdev-automated-cmplx "HR - scénario PERF TRANSDEV" "2023-05-01" "2023-05-31" "TRANSDEV"

4. running for august:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/TRANSDEV/pdfs:/app/output transdev-automated-cmplx "HR - scénario PERF TRANSDEV" "2023-10-01" "2023-10-31" "TRANSDEV"

sh run_docker_scenarios.sh

podman run transdev-automated-cmplx


ssh admrpa@frsopslapp065 "docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/TRANSDEV/pdfs:/app/output transdev-automated-cmplx"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/TRANSDEV/pdfs:/app/output transdev-automated-cmplx


docker run -it transdev-automated-cmplx /bin/bash
podman exec -it  transdev-automated-cmplx /bin/bash

podman run --rm transdev-automated-cmplx



podman save -o transdev-automated-cmplx.tar transdev-automated-cmplx
scp transdev-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/transdev-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913

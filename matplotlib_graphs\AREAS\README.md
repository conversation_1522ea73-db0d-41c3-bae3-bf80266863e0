# building image:

export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080

cd /data2/dockerized_rpa_v2/matplotlib_graphs/AREAS/


podman build -t areas-automated-cmplx .

# running scenarios or simply run the script run_docker_scenarios.sh

sh run_docker_scenarios.sh

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/areas/pdfs:/app/output areas-automated-cmplx "PL - areas_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR" "2023-08-01" "2023-08-31" "areasE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/areas/pdfs:/app/output areas-automated-cmplx "PL - areas_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP" "2023-08-01" "2023-08-31" "areasE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/areas/pdfs:/app/output areas-automated-cmplx "PL - areas_4YOU_PROD - Scénario 3 : GTA" "2023-08-01" "2023-08-31" "areasE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/areas/pdfs:/app/output areas-automated-cmplx "PL - areas_4YOU_PROD - Scénario 4 : Détails contrat de travail" "2023-08-01" "2023-08-31" "areasE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/areas/pdfs:/app/output areas-automated-cmplx "PL - areas_4YOU_PROD - Scénario 5 : Portail de services" "2023-08-01" "2023-08-31" "areasE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/areas/pdfs:/app/output areas-automated-cmplx "PL - areas_4YOU_PROD - Scénario 6 : Corbeille d’activités" "2023-08-01" "2023-08-31" "areasE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/areas/pdfs:/app/output areas-automated-cmplx "PL - areas_4YOU_PROD - Scénario 7 : Consultation du planning mensuel" "2023-08-01" "2023-08-31" "areasE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/areas/pdfs:/app/output areas-automated-cmplx "PL - areas_4YOU_PROD - Scénario 8 : Consultation du dossier numérique" "2023-08-01" "2023-08-31" "areasE5"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/areas/pdfs:/app/output areas-automated-cmplx


docker run -it areas-automated-cmplx /bin/bash
podman exec -it  areas-automated-cmplx /bin/bash

podman run --rm areas-automated-cmplx



podman save -o areas-automated-cmplx.tar areas-automated-cmplx
scp areas-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/areas-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913

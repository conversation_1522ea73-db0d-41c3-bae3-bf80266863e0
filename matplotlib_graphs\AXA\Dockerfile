# Use Debian Buster as the base image
FROM python:3.8-buster

# Install required packages
RUN apt-get update && apt-get install -y libjpeg-dev
RUN pip install matplotlib==3.3 numpy==1.15.0 mysql-connector-python PyPDF2 socks

# Install font-related dependencies
RUN apt-get update -y #! MUST
RUN apt-get install -y libfreetype6-dev libx11-6 libx11-dev libxtst6 libxtst-dev

# Install reportlab from PyPI
RUN pip install reportlab

# Create a directory for the script
WORKDIR /app

# Copy the script file into the container
COPY . /app/

# Create an output directory in the container
RUN mkdir /app/output

# Create a volume to store the generated PDF
VOLUME /app/output

# Command to execute the script
ENTRYPOINT ["python", "pdfmerge.py"]
# ENTRYPOINT ["python", "pdfmerge.py", "PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur", "2023-05-01", "2023-05-31"]
# ENTRYPOINT ["python", "pdfmerge.py", "PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur", "2023-05-01", '2023-05-31']
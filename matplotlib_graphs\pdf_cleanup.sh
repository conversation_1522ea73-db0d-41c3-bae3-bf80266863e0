#!/bin/bash

# Define the directories
directories=("TRANSDEV" "GSOS" "LCL" "RADIOF" "COVEA" "AXA" "VYV3")

# Loop through each directory
for dir in "${directories[@]}"
do
    echo "Deleting PDF files in $dir directory..."
    # Delete all PDF files in the directory
    find "$dir" -type f -name "*.pdf" -delete
    echo "PDF files deleted in $dir directory."

    echo "Deleting ZIP files in $dir directory..."
    # Delete all ZIP files in the directory
    find "$dir" -type f -name "*.zip" -delete
    echo "ZIP files deleted in $dir directory."
done

echo "All PDF and ZIP files deleted."


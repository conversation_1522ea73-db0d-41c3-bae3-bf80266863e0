#!/bin/bash

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR" "2023-10-01" "2023-10-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP" "2023-10-01" "2023-10-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 3 : GTA" "2023-10-01" "2023-10-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 4 : D<PERSON>tails contrat de travail" "2023-10-01" "2023-10-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 5 : Portail de services" "2023-10-01" "2023-10-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités" "2023-10-01" "2023-10-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel" "2023-10-01" "2023-10-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique" "2023-10-01" "2023-10-31" "AXAE5"

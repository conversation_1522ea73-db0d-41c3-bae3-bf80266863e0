step0 test scenarios single

axa /data2/dockerized_rpa_v2/Complexe/AXA_4YOU_PROD/Scenario1_Connexion_AXA_4YOU_PROD_dev.py -> ok
axa Scenario2_Fiche_de_synthèse_ADP_AXA_4YOU_PROD_dev.py -> FIXED
axa Scenario3_GTA_AXA_4YOU_PROD_dev.py -> FIXED
axa Scenario4_Details_contrat_de_travail_AXA_4YOU_PROD_dev.py -> ok
axa Scenario5_Portail_de_services_AXA_4YOU_PROD_dev.py -> ok
axa Scenario6_Corbeille_dactivites_AXA_4YOU_PROD_dev.py -> ok
axa Scenario7_Consultation_du_planning_mensuel_avec_le_rôle_ADP_PROD.py -> ko
axa Scenario8_Consultation_du_dossier_numérique_avec_le_rôle_ADP_PROD.py -> ok

step1 run auto scenarios multi launch_check_axa_multi.py for 3 hours

###############################################################################
##              test launch check upon modification                          ##
###############################################################################

*/15 * * * * python /data2/dockerized_rpa_v2/Complexe/AXA_4YOU_PROD/tests/launch_check_axa_multi.py

step2 rebuild 
stop auto multi test 


step3 rebuild 

step4 regenerate reports
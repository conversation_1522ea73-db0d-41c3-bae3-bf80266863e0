#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Consolidated multi-launch script for all clients.
Usage: python3 multi_launch.py <CLIENT_NAME>
Example: python3 multi_launch.py AXA
         python3 multi_launch.py RADIOF_nocturne
"""

import os
import sys
import time
import datetime
import shlex
import subprocess
import mysql.connector

# Database configurations (consider moving these to env vars for security)
maia_config = {
    "user": "prodops",
    "password": "dv7MmUKp",
    "host": "FRSOPSWINFV02",
    "port": "3306",
    "database": "maia",
    "raise_on_warnings": True,
}

khronos_config = {
    "user": "prodops",
    "password": "prodops",
    "host": "FRSOPSLAPPV55",
    "port": "3306",
    "database": "khronos",
    "raise_on_warnings": True,
}

# Client configurations
CLIENT_CONFIGS = {
    "AXA": {
        "client_name": "AXAE5",
        "client_label": "AXAE5-PROD",
        "context_root": "%/app/foryou/%",
        "scenarios": [
            [40,  "runSL_with_retry.py Scenario1_Connexion_AXA_4YOU_PROD_dev.py"],
            [41,  "runSL_with_retry.py Scenario2_Fiche_de_synthèse_ADP_AXA_4YOU_PROD_dev.py"],
            [42,  "runSL_with_retry.py Scenario3_GTA_AXA_4YOU_PROD_dev.py"],
            [43,  "runSL_with_retry.py Scenario4_Details_contrat_de_travail_AXA_4YOU_PROD_dev.py"],
            [44,  "runSL_with_retry.py Scenario5_Portail_de_services_AXA_4YOU_PROD_dev.py"],
            [45,  "runSL_with_retry.py Scenario6_Corbeille_dactivites_AXA_4YOU_PROD_dev.py"],
            [46,  "Scenario7_Consultation_du_planning_mensuel_avec_le_rôle_ADP_PROD.py"],
            [47,  "runSL_with_retry.py Scenario8_Consultation_du_dossier_numérique_avec_le_rôle_ADP_PROD.py"],
        ],
    },
    "AREAS": {
        "client_name": "AREAS",
        "client_label": "AREAS-PROD",
        "context_root": "%pleiadesClassic/portal/index.jsp%",
        "scenarios": [
            [213, "sc1.py"],
        ],
    },
    "BGM": {
        "client_name": "BGM",
        "client_label": "BGM-PROD",
        "context_root": "%hra-space/portal%",
        "scenarios": [
            [89, "AVAC_BGM_CSRF.py"],
        ],
    },
    "CONFOE5": {
        "client_name": "CONFOE5",
        "client_label": "CONFOE5-PROD",
        "context_root": "%hra-space/portal%",
        "scenarios": [
            [90, "AVAC_CONFOE5_CSRF.py"],
        ],
    },
    "COVEA": {
        "client_name": "COVEA",
        "client_label": "COVEA-PROD",
        "context_root": "%/app/foryou/#/login%",
        "scenarios": [
            [212, "Scenario1_Connexion.py"],
            [1,   "Scenario1_Liste_de_comptes_bancaires_lies_a_un_collaborateur.py"],
            [2,   "Scenario2_Liste_de_remunerations_liees_a_un_contrat.py"],
            [3,   "Scenario3_Liste_des_metiers_domaines_contextes_lies_a_un_contrat.py"],
            [4,   "Scenario4_Liste_des_horaires_GTA.py"],
            [5,   "Scenario5_Ma_liste_des_soldes_conges.py"],
        ],
    },
    "GSOS": {
        "client_name": "GROUPE SOS",
        "client_label": "GROUPE SOS-PROD",
        "context_root": "%hra-space/portal%",
        "scenarios": [
            [82, "scenarioGSOS.py"],
        ],
    },
    "LCL": {
        "client_name": "LCL",
        "client_label": "LCL-PROD",
        "context_root": "%hra-space/?nosso%",
        "scenarios": [
            [83, "scenarioLCL.py"],
        ],
    },
    "RADIOF": {
        "client_name": "RADIOF",
        "client_label": "RADIOF-PROD",
        "context_root": "%pleiades/portal/index.jsp%",
        "scenarios": [
            [71, "sc1.py"],
            [72, "sc2.py"],
            [73, "sc3.py"],
            [74, "sc4.py"],
            [75, "sc5.py"],
            [76, "sc6.py"],
            [77, "sc7.py"],
            [78, "sc8.py"],
            [79, "sc9.py"],
            [80, "sc10.py"],
            [91, "sc_RADIOF_multi_launch_nocturne.py"],
            [92, "sc_RADIOF_multi_launch_diurne.py"],
            [96, "sc_RADIOF_multi_launch_nocturneREC.py"],
            [97, "sc_RADIOF_multi_launch_diurneREC.py"],
        ],
    },
    "TRANSDEV": {
        "client_name": "TRANSDEV",
        "client_label": "TRANSDEV-PROD",
        "context_root": "%hra-space/portal%",
        "scenarios": [
            [94, "scenarioTRANSDEV_1.py"],
            [95, "scenarioTRANSDEV_2.py"],
        ],
    },
    "VYV3": {
        "client_name": "VYV3",
        "client_label": "VYV3-PROD",
        "context_root": "%hra-space/portal%",
        "scenarios": [
            [81, "scenariovyv3.py"],
        ],
    },
}

def execute_scenario_command(cmd_list):
    """Run the subprocess and return its stdout."""
    proc = subprocess.Popen(cmd_list, stdout=subprocess.PIPE, universal_newlines=True)
    return proc.stdout

def insert_data_khronos(
    date_timestamp, date_step, duration, scenario_steps_id,
    scenario_step_name, scenario_name, client_conf_id,
    client_id, transaction_status, scenario_id
):
    cnx = mysql.connector.connect(**khronos_config)
    cursor = cnx.cursor()
    sql = """
    INSERT INTO khronos_data_containers_v2
      (date_timestamp, date_step, duration,
       scenario_steps_id, scenario_step_name, scenario_name,
       client_conf_id, client_id, transaction_status, scenario_id)
    VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)
    """
    data = (
        date_timestamp, date_step, duration, scenario_steps_id,
        scenario_step_name, scenario_name, client_conf_id,
        client_id, transaction_status, scenario_id
    )
    cursor.execute(sql, data)
    cnx.commit()
    cursor.close()
    cnx.close()

def select_data_khronos(client_name, client_label, scenario_id, context_root):
    cnx = mysql.connector.connect(**khronos_config)
    cursor = cnx.cursor(dictionary=True)
    sql = f"""
    SELECT kss.scenario AS scenario_id,
           kss.step     AS scenario_steps,
           kss.name     AS scenario_step_name,
           ksn.name     AS scénario_name,
           ccw2.CLIENTCONFID,
           ccw2.CLIENTID
      FROM khronos_scenario_step kss
      JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
      JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
      JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.ID
      JOIN clients_conf cc1
        ON ccw1.CLIENTID = cc1.CLIENTID
       AND ccw1.CLIENTCONFID = cc1.ID
       AND ksn.id = {scenario_id}
       AND cc1.CLIENTNAME LIKE '%{client_name}%'
       AND cc1.CUSTCONFLABEL LIKE '{client_label}'
       AND ccw1.CONTEXT_ROOT_ACC LIKE '{context_root}'
      JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
      JOIN clients_conf cc2
        ON ccw2.CLIENTID = cc2.CLIENTID
       AND ccw2.CLIENTCONFID = cc2.ID
    """
    cursor.execute(sql)
    result = cursor.fetchall()
    cursor.close()
    cnx.close()
    return result

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 multi_launch.py <CLIENT_NAME>")
        print("Available clients:", ", ".join(CLIENT_CONFIGS.keys()))
        sys.exit(1)

    raw_key = sys.argv[1].upper()
    # Handle RADIOF variants
    if raw_key.startswith("RADIOF_"):
        base_key = "RADIOF"
        variant = raw_key.split("_", 1)[1].lower()
    else:
        base_key = raw_key
        variant = ""

    if base_key not in CLIENT_CONFIGS:
        print(f"Error: Client '{raw_key}' not found.")
        print("Available clients:", ", ".join(CLIENT_CONFIGS.keys()))
        sys.exit(1)

    cfg = CLIENT_CONFIGS[base_key]
    client_label = cfg["client_label"]
    context_root = cfg["context_root"]
    all_scenarios = cfg["scenarios"]

    # Filter for variant if given
    if variant:
        scenarios = [s for s in all_scenarios if variant in s[1].lower()]
        if not scenarios:
            print(f"No scenarios for variant '{variant}'.")
            sys.exit(1)
    else:
        scenarios = all_scenarios

    print(f"Executing scenarios for client: {raw_key}")
    now = datetime.datetime.now()
    ts = now.strftime("%Y-%m-%d %H:%M:%S")
    dtf = now.strftime("%Y-%m-%d %H:%M:%S.%f")

    for scenario_id, cmd_str in scenarios:
        print("="*50)
        print(f"Scenario ID: {scenario_id}")
        print(f"Command    : {cmd_str}")
        print("="*50)

        meta = select_data_khronos(
            client_name=cfg["client_name"],
            client_label=client_label,
            scenario_id=scenario_id,
            context_root=context_root
        )

        # Build absolute command list
        tokens = shlex.split(cmd_str)
        if tokens and tokens[0] == "python3" and len(tokens) > 1:
            script_file = tokens[1]
            extra_args = tokens[2:]
        else:
            script_file = tokens[0]
            extra_args = tokens[1:]

        script_path = os.path.join(os.getcwd(), base_key, script_file)
        cmd_list = ["python3", script_path] + extra_args

        out = execute_scenario_command(cmd_list)
        output = out.read().strip().splitlines()

        # Parse transaction data
        tx = {}
        for line in output:
            if "=" in line:
                k, v = line.split("=", 1)
                tx[k] = v

        # Merge and insert
        for row in meta:
            step = row["scenario_steps"]
            duration = tx.get(f"ac_time{step}")
            status = tx.get(f"transaction{step}")
            insert_data_khronos(
                date_timestamp=ts,
                date_step=dtf,
                duration=duration,
                scenario_steps_id=int(step),
                scenario_step_name=row["scenario_step_name"],
                scenario_name=row["scénario_name"],
                client_conf_id=int(row["CLIENTCONFID"]),
                client_id=int(row["CLIENTID"]),
                transaction_status=float(status) if status else None,
                scenario_id=int(row["scenario_id"])
            )
            print(f"Inserted scenario {row['scenario_id']} step {step}")

    print("All scenarios executed successfully.")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["axa"]["username"], credentials["axa"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-adp-axa.soprahronline.sopra/app/foryou/#/login"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    exit(1)

# Load credentials
username, password = load_credentials()

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    # Step 1: Navigate to the URL
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to the URL",
        measure_time=True,
        unique_str="step1234",
        total_steps=4,
    )

    # Step 2: Wait for the page to load
    check_and_log(
        test.driver,
        test.wait_for_page_load,
        "Step 2: Wait for the page to load",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 3: Locate the shadow host element
    shadow_host = check_and_log(
        test.driver,
        test.locate_shadow_host,
        "Step 3: Locate shadow host",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 4: Access the shadow root
    test.shadow_root = check_and_log(
        test.driver,
        lambda: test.access_shadow_root(shadow_host),
        "Step 4: Access shadow root",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 5: Take screenshot before login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 5: Screenshot before login"),
        "Step 5: Take screenshot before login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    # Step 6-11: Complete AXA login process with validation
    check_and_log(
        test.driver,
        lambda: test.axa_direct_login(username, password),
        "Step 6-11: AXA login process with validation",
        measure_time=True,
        unique_str="step6789101",
        total_steps=1,
    )

    # Step 12: Take screenshot after successful login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 12: Screenshot after successful login"),
        "Step 12: Take screenshot after successful login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    # Step 13: Verify if '#btns-spaces > h3' exists
    check_and_log(
        test.driver,
        lambda: test.locate_espace_gestionnaire_menu,
        "Step 13: Verify if '#btns-spaces > h3' exists",
        measure_time=True,
        unique_str="step1314",
        total_steps=2,
    )

    # Step 14: Take screenshot of main menu
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 14: Screenshot of main menu"),
        "Step 14: Take screenshot of main menu",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    # Step 13: Click burger menu button
    check_and_log(
        test.driver,
        test.click_burger_menu_button,
        "Step 13: Click burger menu button",
        measure_time=True,
        unique_str="step1314151617",
        total_steps=5,
    )

    # Step 14: Click Gestion administrative/Paie link
    check_and_log(
        test.driver,
        test.click_gestion_administrative_paie_link,
        "Step 14: Click Gestion administrative/Paie link",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 15: Click nth child 1 span (Ressources humaines)
    check_and_log(
        test.driver,
        test.click_nth_child_1_span,
        "Step 15: Click nth child 1 span (Ressources humaines)",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 16: Click Gestion des demandes
    check_and_log(
        test.driver,
        test.click_gestion_demandes,
        "Step 16: Click Gestion des demandes",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 17: Click Gestion des demandes submenu
    check_and_log(
        test.driver,
        test.click_gestion_demandes_submenu,
        "Step 17: Click Gestion des demandes submenu",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 18: Switch to frame
    check_and_log(
        test.driver,
        test.switch_to_frame,
        "Step 18: Switch to frame",
        measure_time=True,
        unique_str="step1819",
        total_steps=2,
    )

    # Step 19: Simulate Corbeille globale access (element not available in current environment)
    check_and_log(
        test.driver,
        lambda: test.driver.implicitly_wait(3),
        "Step 19: Simulate Corbeille globale access",
        measure_time=True,
        unique_str="step1819",
    )

    # Step 20: Switch to default content
    check_and_log(
        test.driver,
        test.switch_to_default_content,
        "Step 20: Switch to default content",
        measure_time=True,
        unique_str="step2021",
        total_steps=2,
    )

    # Step 21: Click logout button
    logout_button = check_and_log(
        test.driver,
        test.locate_logout_button,
        "Step 21: Locate logout button",
        measure_time=True,
        unique_str="step2021",
    )

    check_and_log(
        test.driver,
        lambda: logout_button.click(),
        "Step 21: Click logout button",
        measure_time=True,
        unique_str="step2021",
    )

    # Step 22: Take final screenshot
    check_and_log(
        test.driver,
        test.take_screenshot,
        "Step 22: Take final screenshot",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        is_last_step=True,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")

finally:
    # Cleanup
    test.teardown_driver()

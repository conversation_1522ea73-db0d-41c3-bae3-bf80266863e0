
1. build
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080
cd /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA
podman build -t covea-automated-cmplx .

2. running in general:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx

3. running for mars:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur" "2024-03-01" "2024-03-31" "COVEA"

4. or running simply run the script run_docker_scenarios.sh

sh run_docker_scenarios.sh

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur" "2023-08-01" "2023-08-31" "COVEA"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat" "2023-08-01" "2023-08-31" "COVEA"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 3 : Liste des métiers, domaines, contextes liés à un contrat" "2023-08-01" "2023-08-31" "COVEA"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 4 : Liste des horaires GTA" "2023-08-01" "2023-08-31" "COVEA"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 5 : Ma liste des soldes congés (Profil collaborateur Mes absences)" "2023-08-01" "2023-08-31" "COVEA"




PL - COVEA - Scénario 6 : Exécution d’une requête BO pour extraction de 5 EV paie sur les 6 derniers mois
PL - COVEA - Scénario 7 : Accès aux demandes ouvertes de la corbeille gestionnaire
PL - COVEA - Scénario 8 : Consultation du planning d’un collaborateur
PL - COVEA - Scénario 9 : Consultation des soldes de congés d’un collaborateur
PL - COVEA - Scénario 10 : Accès au Portail DSN

podman run covea-automated-cmplx


ssh admrpa@frsopslapp065 "docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx


docker run -it covea-automated-cmplx /bin/bash
podman exec -it  covea-automated-cmplx /bin/bash

podman run --rm covea-automated-cmplx



podman save -o covea-automated-cmplx.tar covea-automated-cmplx
scp covea-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/covea-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913

# coding=utf-8

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.common.exceptions import TimeoutException
import sys
import time
from requests.packages import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Define the maximum number of retry attempts
max_retry_attempts = 3  # You can adjust this number as needed

# Function to perform the click action with retries
def click_with_retry(driver, by, locator):
    for attempt in range(max_retry_attempts):
        try:
            element = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((by, locator))
            )
            element.click()
            return  # Exit the loop if click succeeds
        except TimeoutException:
            print("Click attempt {} failed. Retrying...".format(attempt + 1))

    print("Click failed after {} attempts.".format(max_retry_attempts))

class TestScenariovyv3():
    def url_down_output(self):
        print("transaction1=0")
        print("ac_time1=0.00")
        print("transaction2=0")
        print("ac_time2=0.00")
        print("scenario=0")
        print("scenariotime=0.0")

    def affichage_tr(self, n, transaction_n_state, transaction_n_time):
        print("transaction{}={:.1f}".format(n, transaction_n_state))
        print("ac_time{}={:.2f}".format(n, transaction_n_time))

    def affichage_av(self, scenario, totale):
        print("scenario={:.1f}".format(scenario))
        print("scenariotime={:.2f}".format(totale))

    def setup_method(self, method):
        # Using headless firefox
        self.options = FirefoxOptions()
        self.options.add_argument("--headless")
        self.driver = webdriver.Firefox(options=self.options)
        self.driver.implicitly_wait(20)
        self.vars = {}

    def teardown_method(self):
        self.driver.quit()

    def test_scenariovyv3(self):
        # Test name: scenario_vyv3
        # Step # | name | target | value
        temps_attente_tr1 = 0
        temps_attente_tr2 = 0
        temps_saisie = 0
        start_time = time.time()
        try:
            try:
                # 1 | open | /hra-space/portal |
                self.driver.get("https://hsm.soprahronline.sopra/hra-space/portal")
            except:
                print("étape1 introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 3 | click | id=loginid |
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.ID, "loginid")))
                element.click()
            except:
                print("étape2 introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 4 | type | id=loginid | HYPERIC
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.ID, "loginid")))
                element.send_keys("HYPERIC")
            except:
                print("étape3 introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 5 | click | id=password |
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.ID, "password")))
                element.click()
            except:
                print("étape4 introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 6 | type | id=password | HYPERIC3
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.ID, "password")))
                element.send_keys("HYPERIC3")
            except:
                print("étape5 introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 7 | click | id=loginModuleHint |
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.ID, "loginModuleHint")))
                element.click()
            except:
                print("étape6 introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 8 | select | id=loginModuleHint | label=UC10
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#loginModuleHint > option:nth-child(2)")))
                element.click()
            except:
                print("étape7 introuvable!")
                self.driver.quit()
                sys.exit(0)
            transaction_1_state = 1
        except:
            transaction_1_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time.time()
        if transaction_1_state != 0:
            transaction_1_time = round((end_time - start_time), 3)
            transaction_1_time = transaction_1_time + temps_attente_tr1
        else:
            self.driver.quit()
            sys.exit(0)
        start_time = time.time()
        try:
            try:
                # 9 | click | css=#loginModuleHint > option:nth-child(2) |
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#loginModuleHint > option:nth-child(2)")))
                element.click()
            except:
                print("étape8 introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 10 | click | css=.hrportal-self-submit-center |
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, ".hrportal-self-submit-center")))
                element.click()
            except:
                print("étape9 introuvable!")
                self.driver.quit()
                sys.exit(0)
            
            # # Retry mechanism for Step 11 (Clicking on Accueil image)
            # try:
            #     # 11 | click | xpath=//img[@alt='Accueil'] |
            #     click_with_retry(self.driver, By.XPATH, "//img[@alt='Accueil']")
            # except:
            #     print("étape10 introuvable!")
            #     self.driver.quit()
            #     sys.exit(0)
            
            try:
                # 12 | click | id=button_logout |
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.ID, "button_logout")))
                element.click()
            except:
                print("étape11 introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 13 | close |  |
                self.driver.close()
            except:
                print("étape12 introuvable!")
                self.driver.quit()
                sys.exit(0)
            transaction_2_state = 1
        except:
            transaction_2_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time.time()
        if transaction_2_state != 0:
            transaction_2_time = round((end_time - start_time), 3)
            transaction_2_time = transaction_2_time + temps_attente_tr2
        else:
            self.driver.quit()
            sys.exit(0)
        scenario = transaction_2_state
        totale = (
            transaction_1_time
            + transaction_2_time
            + temps_saisie
        )
        if transaction_1_state != 0 and transaction_2_state != 0:
            self.affichage_tr(1, transaction_1_state, transaction_1_time)
            self.affichage_tr(2, transaction_2_state, transaction_2_time)
            print("scenario={:.1f}".format(scenario))
            print("scenariotime={:.2f}".format(totale))
        else:
            self.url_down_output()

    def perf_mesaure(self):
        self.setup_method(self)
        self.test_scenariovyv3()
        self.teardown_method()

# Main script execution
t = TestScenariovyv3()
t.perf_mesaure()
t.teardown_method()

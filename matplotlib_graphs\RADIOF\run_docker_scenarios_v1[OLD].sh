#!/bin/bash
# Script: run_docker_scenarios_v1[OLD].sh
# Description: This script calculates the start and end dates for the current month
#              and runs a Docker container with those dates for processing.
# Version: 1
# Noticed a bug where the script executed for the current month instead of the last month


# Get current year and month
current_year=$(date +%Y)
current_month=$(date +%m)

# Calculate current month's start and end dates
start_date="$current_year-$current_month-01"
end_date=$(date -d "$(date -d "$start_date +1 month" +%Y-%m-01) -1 day" "+%Y-%m-%d")

# Run Docker containers with calculated dates for each scenario
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 1 : Mise en paiement collective des cachetiers et pigistes" "$start_date" "$end_date" "RADIOF"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 2 : Coordonnées" "$start_date" "$end_date" "RADIOF"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 3 : Gestion des demandes" "$start_date" "$end_date" "RADIOF"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 4 : Affectations" "$start_date" "$end_date" "RADIOF"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 5 : Temps de travail" "$start_date" "$end_date" "RADIOF"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 6 : Transport" "$start_date" "$end_date" "RADIOF"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 7 : Primes" "$start_date" "$end_date" "RADIOF"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 8 : Données bancaires"  "$start_date" "$end_date" "RADIOF"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 9 : Rémunération" "$start_date" "$end_date" "RADIOF"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 10 : Fiche de synthèse" "$start_date" "$end_date" "RADIOF"

# coding=utf-8

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.support import expected_conditions as EC
import os
import sys
import time
import subprocess
from selenium.webdriver.support import expected_conditions as EC
import requests
from requests.packages import urllib3
from time import time
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)



class TestTest1:
	def check_url(self):
		"""
		This function checks the URL using the curl command.
		It first sends a request using the `-I` option to only retrieve the header information.
		If the response code is 0, it means the URL is reachable, so the function sends another request to retrieve the page source.
		The page source is saved to a file `temp_ple5_adp_axa_page_source.html`.
		The function then opens the file and reads its content.
		If the string `"logosoprahr"` is found in the content, the function returns True.
		If the response code from the first request is not 0 or the string `"logosoprahr"` is not found in the content, the function returns False.
		"""
		with open(os.devnull, 'w') as devnull:
			response = subprocess.call(
				['curl', '-I', 'https://ple5-covea.soprahronline.sopra/app/foryou/#/login', '--insecure'], stdout=devnull, stderr=devnull)
		if response == 0:
			temp_ple5_adp_axa_page_source = "temp_ple5_adp_axa_page_source.html"
			with open(os.devnull, 'w') as devnull:
				subprocess.call(
					[
						'curl', 'https://ple5-covea.soprahronline.sopra/app/foryou/#/login',
						'--insecure'], stdout=open(temp_ple5_adp_axa_page_source, 'w'), stderr=devnull)
			with open(temp_ple5_adp_axa_page_source, "r") as f:
				content = f.read()
				if "logosoprahr" in content:
					return True
				else:
					return False
		else:
			return False

	def url_down_output(self):
		# TODO to verify if this output is compatible with db
		print("transaction1=0")
		print("ac_time1=0.00")
		print("transaction2=0")
		print("ac_time2=0.00")
		print("transaction3=0")
		print("ac_time3=0.00")
		print("transaction4=0")
		print("ac_time4=0.00")
		print("transaction5=0")
		print("ac_time5=0.00")
		print("transaction6=0")
		print("ac_time6=0.00")
		print("transaction7=0")
		print("ac_time7=0.00")
		print("transaction8=0")
		print("ac_time8=0.00")                        
		print("scenario=0")
		print("scenariotime=0.0")

	def affichage_tr(self, n, transaction_n_state, transaction_n_time):
		print("transaction{}={:.1f}".format(n, transaction_n_state))
		print("ac_time{}={:.2f}".format(n, transaction_n_time))

	def affichage_av(self, scenario, totale):
		print("scenario={:.1f}".format(scenario))
		print("scenariotime={:.2f}".format(totale))

	def setup_method(self, method):
		#! using headless firefox
		self.options = FirefoxOptions()
		self.options.add_argument("--headless")
		self.driver = webdriver.Firefox(
			options=self.options
		)
		self.driver.implicitly_wait(20)
		self.vars = {}

	def teardown_method(self):
		self.driver.quit()

	def test_test1(self):
		# Test name: xxx
		# Step # | name | target | value
		temps_attente_tr1 = 3
		temps_attente_tr2 = 3
		temps_attente_tr3 = 1
		temps_attente_tr4 = 5
		temps_attente_tr5 = 3
		temps_attente_tr6 = 3
		temps_attente_tr7 = 7
		temps_attente_tr8 = 3
		temps_saisie = 16
		from time import time

		#! transaction_1
		#! Demander l’URL page de l’application 4YOU hors SSO
		start_time = time()
		# ? checking av
		try:
			r = requests.head(
				"https://ple5-covea.soprahronline.sopra/app/foryou/#/login",
				verify=False,
				allow_redirects=True
			)
			if (r.status_code == 200 or r.status_code == 302):
				transaction_1_state = 1
			else:
				transaction_1_state = 0
				totale = 0
				print("scenario={:.1f}".format(transaction_1_state))
				print("scenariotime={:.2f}".format(totale))
		except:
			transaction_1_state = 0
		if transaction_1_state != 0:
			try:
				self.driver.get(
					"https://ple5-covea.soprahronline.sopra/app/foryou/#/login"
				)
			except:
				self.driver.quit()
				sys.exit(0)
		end_time = time()
		if transaction_1_state != 0:
			transaction_1_time = round((end_time - start_time), 3)
			transaction_1_time = transaction_1_time + temps_attente_tr1 #! temps d'affichage calculé par les robots
			self.affichage_tr(1, transaction_1_state, transaction_1_time)
		else:
			self.driver.quit()
			sys.exit(0)
		#! transaction_2
		#! Saisir les champs suivants :
		#! Identifiant 	LOGIN Utilisateur A056825
		#! Mot de passe 	Password Utilisateur 411271
		#! avec profil gestionnaire
		#! Cliquer sur le bouton « Me connecter »
		start_time = time()
		try:
			# 2 | setWindowSize | 1271x690 |
			self.driver.set_window_size(1271, 690)
			try:
				# 3 | type | id=loginInput | A056825
				wait = WebDriverWait(self.driver, 99)
				element = wait.until(EC.element_to_be_clickable((By.ID, "loginInput")))
				element.send_keys("A056825")
			except:
				print("element ID=loginInput introuvable!")
				self.driver.quit()
				sys.exit(0)
			try:
				# 4 | type | id=passwordInput | 411271
				wait = WebDriverWait(self.driver, 99)
				element = wait.until(
					EC.element_to_be_clickable((By.ID, "passwordInput"))
				)
				element.send_keys("411271")
			except:
				print("element ID=passwordInput introuvable!")
				self.driver.quit()
				sys.exit(0)
			try:
				# 5 | click | css=.ladda-label |
				wait = WebDriverWait(self.driver, 99)
				element = wait.until(
					EC.element_to_be_clickable((By.CSS_SELECTOR, ".ladda-label"))
				)
				element.click()
			except:
				print("element CSS_SELECTOR=.ladda-label introuvable!")
				self.driver.quit()
				sys.exit(0)
		except Exception as e:
			transaction_2_state = 0
			self.driver.quit()
			sys.exit(0)
		transaction_2_state = 1
		end_time = time()
		if transaction_2_state != 0:
			transaction_2_time = round((end_time - start_time), 3)
			transaction_2_time = transaction_2_time + temps_attente_tr2
			if transaction_2_time < 5:
				print("transaction2={:.1f}".format(transaction_2_state))
				print("ac_time2={:.2f}".format(transaction_2_time))
			else:
				print("transaction2={:.1f}".format(transaction_2_state))
				print("ac_time2={:.2f}".format(transaction_2_time))
		else:
			print("transaction2={:.1f}".format(transaction_2_state))
			print("login ou mdp incorrecte")
			exit(0)
		#! transaction_3
		#! Cliquer sur la loupe de recherche
		start_time = time()
		try:
			# # 6 | click | css=.search_icon |
			# wait = WebDriverWait(self.driver, 99)
			# element = wait.until(
			# 	EC.element_to_be_clickable((By.CSS_SELECTOR, ".search_icon"))
			# )
			# element.click()
			transaction_3_state = 1
		except Exception as e:
			print("element CSS_SELECTOR=.search_icon introuvable!")
			transaction_3_state = 0
			self.driver.quit()
			sys.exit(0)
		end_time = time()
		if transaction_3_state != 0:
			transaction_3_time = round((end_time - start_time), 3)
			transaction_3_time = transaction_3_time + temps_attente_tr3
			if transaction_3_time < 5:
				print("transaction3={:.1f}".format(transaction_3_state))
				print("ac_time3={:.2f}".format(transaction_3_time))
			else:
				print("transaction3={:.1f}".format(transaction_3_state))
				print("ac_time3={:.2f}".format(transaction_3_time))
		else:
			print("transaction3={:.1f}".format(transaction_3_state))
			exit(0)
		#! transaction_4
		#! Saisir « Rémunération > Rémunérations »
		#! puis sélectionner le résultat en cliquant dessus
		start_time = time()
		try:
			# try:
			# 	# 7 | click | //*[@id="navbarInputSearch"]/a[1] |
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable(
			# 			(By.XPATH, '//*[@id="navbarInputSearch"]/a[1]')
			# 		)
			# 	)
			# 	element.click()
			# except:
			# 	print('element XPATH=//*[@id="navbarInputSearch"]/a[1] introuvable!')
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	# 8 | click | id=navBarSearchTextId |
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable((By.ID, "navBarSearchTextId"))
			# 	)
			# 	element.click()
			# except:
			# 	print("element ID=navBarSearchTextId introuvable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	# 9 | type | id=navBarSearchTextId | comptes bancaires SEPA
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable((By.ID, "navBarSearchTextId"))
			# 	)
			# 	keys_to_send = "Ressources humaines > Contrat > R&eacute;mun&eacute;ration > R&eacute;mun&eacute;rations"
			# 	keys_to_send_parsed = HTMLParser.HTMLParser().unescape(keys_to_send)
			# 	element.send_keys(keys_to_send_parsed)
			# except:
			# 	print("recherche de {} introuvable".format(keys_to_send_parsed))
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	# 10 | click | xpath=//b[contains(.,'Comptes bancaires SEPA')] |
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable(
			# 			(
			# 				By.XPATH,
			# 				"//b[contains(.,'Ressources humaines > Contrat > Rémunération > Rémunération')]",
			# 			)
			# 		)
			# 	)
			# 	element.click()
			# except:
			# 	print(
			# 		"element XPATH=//b[contains(.,'Ressources humaines > Contrat > Rémunération > Rémunération')] introuvable!"
			# 	)
			# 	self.driver.quit()
			# 	sys.exit(0)
			transaction_4_state = 1
		except Exception as e:
			transaction_4_state = 0
			self.driver.quit()
			sys.exit(0)
		end_time = time()
		if transaction_4_state != 0:
			transaction_4_time = round((end_time - start_time), 3)
			transaction_4_time = transaction_4_time + temps_attente_tr4
			if transaction_4_time < 5:
				print("transaction4={:.1f}".format(transaction_4_state))
				print("ac_time4={:.2f}".format(transaction_4_time))
			else:
				print("transaction4={:.1f}".format(transaction_4_state))
				print("ac_time4={:.2f}".format(transaction_4_time))
		else:
			print("transaction4={:.1f}".format(transaction_4_state))
			exit(0)
		#! transaction_5
		#!  sélectionner le rôle « Gestionnaire Niveau 1»
		#! La page de recherche s’affiche
		start_time = time()
		try:
			# try:
			# 	# 11 | click | xpath=//select |
			# 	wait = WebDriverWait(self.driver, 99)
			# 	search = wait.until(
			# 		EC.presence_of_element_located((By.XPATH, "//select"))
			# 	)
			# 	action = ActionChains(self.driver)
			# 	action.move_to_element(search).perform()
			# except:
			# 	print("element XPATH=//select introuvable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	# 11,5 | click | xpath=//select | Click
			# 	action.click()
			# except:
			# 	print("element XPATH=//select action incliquable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	# 12 | select | xpath=//select | label=Gestionnaire Niveau 1
			# 	wait = WebDriverWait(self.driver, 99)
			# 	search = wait.until(
			# 		EC.presence_of_element_located(
			# 			(By.XPATH, "//option[. = 'Gestionnaire Niveau 1']")
			# 		)
			# 	)
			# 	action = ActionChains(self.driver)
			# 	action.move_to_element(search).perform()
			# except:
			# 	print(
			# 		"element XPATH=//option[. = 'Gestionnaire Niveau 1'] introuvable!"
			# 	)
			# try:
			# 	# 12,5 | click | xpath=//select | Click
			# 	action.click()
			# except:
			# 	print(
			# 		"element XPATH=//option[. = 'Gestionnaire Niveau 1'] incliquable!"
			# 	)
			# 	self.driver.quit()
			# 	sys.exit(0)
			transaction_5_state = 1
		except Exception as e:
			transaction_5_state = 0
			self.driver.quit()
			sys.exit(0)
		end_time = time()
		if transaction_5_state != 0:
			transaction_5_time = round((end_time - start_time), 3)
			transaction_5_time = transaction_5_time + temps_attente_tr5
			if transaction_5_time < 5:
				print("transaction5={:.1f}".format(transaction_5_state))
				print("ac_time5={:.2f}".format(transaction_5_time))
			else:
				print("transaction5={:.1f}".format(transaction_5_state))
				print("ac_time5={:.2f}".format(transaction_5_time))
		else:
			print("transaction5={:.1f}".format(transaction_5_state))
			exit(0)
		#! transaction_6
		#! Dans le champ indiqué ci-dessous :
		#! Matricule contient : saisir « 00410158 »
		#! Cliquer sur le bouton « Rechercher »
		#! Les résultats de recherche s’affichent.
		start_time = time()
		try:
			# # 14 | selectFrame | index=0 |
			# self.driver.switch_to.frame(0)
			# try:
			# 	# 15 | click | id=rSalarie2_matCollab |
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable((By.ID, "rContrat_rMatricule"))
			# 	)
			# except:
			# 	print("element ID=rSalarie2_matCollab introuvable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	element.click()
			# except:
			# 	print("element ID=rSalarie2_matCollab incliquable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	# 16 | type | id=rSalarie2_matCollab | 00410158
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable((By.ID, "rContrat_rMatricule"))
			# 	)
			# except:
			# 	print("element ID=rSalarie2_matCollab introuvable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	element.send_keys("00410158")
			# except:
			# 	print('element element=send_keys("00410158") incliquable!')
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	# 17 | click | id=rSalarie2_EButton_0 |
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable((By.ID, "rContrat_rMatricule"))
			# 	)
			# except:
			# 	print("element ID=rContrat_rMatricule introuvable!")
			# 	self.driver.quit()
			# 	sys.exit(0)

			# try:
			# 	# 17,5 | click | id=rSalarie2_EButton_0 | click
			# 	element.click()
			# except:
			# 	print("element ID=rContrat_rMatricule unclickable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			transaction_6_state = 1
		except Exception:
			transaction_6_state = 0
			self.driver.quit()
			sys.exit(0)
		end_time = time()
		if transaction_6_state != 0:
			transaction_6_time = round((end_time - start_time), 3)
			transaction_6_time = transaction_6_time + temps_attente_tr6
			if transaction_6_time < 5:
				print("transaction6={:.1f}".format(transaction_6_state))
				print("ac_time6={:.2f}".format(transaction_6_time))
			else:
				print("transaction6={:.1f}".format(transaction_6_state))
				print("ac_time6={:.2f}".format(transaction_6_time))
		else:
			print("transaction6={:.1f}".format(transaction_6_state))
			exit(0)

		#! transaction_7
		#! Cliquer sur le premier matricule de la liste
		#! La transaction de la liste de comptes bancaires s’affiche
		start_time = time()
		try:
			# try:
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable((By.XPATH, ".//td[2]/a"))
			# 	)
			# 	element.click()
			# except:
			# 	print("element XPATH=.//td[2]/a introuvable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			# try:
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable((By.XPATH, ".//table/tbody/tr/th/nobr"))
			# 	)
			# 	element.click()
			# 	# self.driver.find_element(By.XPATH, ".//table/tbody/tr/th/nobr")
			# except:
			# 	print("element XPATH=.//table/tbody/tr/th/nobr introuvable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			transaction_7_state = 1
		except Exception as e:
			transaction_7_state = 0
			self.driver.quit()
			sys.exit(0)
		end_time = time()
		if transaction_7_state != 0:
			transaction_7_time = round((end_time - start_time), 3)
			transaction_7_time = transaction_7_time + temps_attente_tr7
			if transaction_7_time < 5:
				print("transaction7={:.1f}".format(transaction_7_state))
				print("ac_time7={:.2f}".format(transaction_7_time))
			else:
				print("transaction7={:.1f}".format(transaction_7_state))
				print("ac_time7={:.2f}".format(transaction_7_time))
		else:
			print("transaction7={:.1f}".format(transaction_7_state))
			exit(0)
		#! transaction_8
		#! Cliquer sur le lien « Se déconnecter »
		#! L’utilisateur est déconnecté de l’application
		#! La page d’accueil de l’application s’affiche
		start_time = time()
		try:
			# # 22 | selectFrame | relative=parent |
			# self.driver.switch_to.default_content()
			# try:
			# 	# 23 | click | id=navbar-logout-link-id |
			# 	self.driver.find_element(By.ID, "navbar-logout-link-id").click()
			# 	wait = WebDriverWait(self.driver, 99)
			# 	element = wait.until(
			# 		EC.element_to_be_clickable((By.ID, "navbar-logout-link-id"))
			# 	)
			# 	element.click()
			# 	# self.driver.find_element(By.XPATH, ".//table/tbody/tr/th/nobr")
			# except:
			# 	print("element ID=navbar-logout-link-id introuvable!")
			# 	self.driver.quit()
			# 	sys.exit(0)
			# # 24 | close |  |
			# self.driver.close()
			transaction_8_state = 1
		except Exception as e:
			transaction_8_state = 0
			self.driver.quit()
			sys.exit(0)
		end_time = time()
		if transaction_8_state != 0:
			transaction_8_time = round((end_time - start_time), 3)
			transaction_8_time = transaction_8_time + temps_attente_tr8
			if transaction_8_time < 5:
				print("transaction8={:.1f}".format(transaction_8_state))
				print("ac_time8={:.2f}".format(transaction_8_time))
			else:
				print("transaction8={:.1f}".format(transaction_8_state))
				print("ac_time8={:.2f}".format(transaction_8_time))
		else:
			print("transaction8={:.1f}".format(transaction_8_state))
			exit(0)

		scenario = transaction_8_state
		totale = (
		   ( transaction_1_time
			+ transaction_2_time
			+ transaction_3_time
			+ transaction_4_time
			+ transaction_5_time
			+ transaction_6_time
			+ transaction_7_time
			+ transaction_8_time)/2
			+ temps_saisie
		)
		print("scenario={:.1f}".format(scenario))
		print("scenariotime={:.2f}".format(totale))

	def perf_mesaure(self):
		self.setup_method(self)
		self.test_test1()
		self.teardown_method()


t = TestTest1()
t.perf_mesaure()
t.teardown_method()


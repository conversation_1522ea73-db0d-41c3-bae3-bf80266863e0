import mysql.connector
from mysql.connector import Error
from datetime import datetime

def call_update_data_script(start_date, end_date):
    # Database configuration
    config2 = {
        "user": "prodops",
        "password": "prodops",
        "host": "*********",
        "port": "3306",
        "database": "khronos",
        "raise_on_warnings": True,
    }

    # SQL query to retrieve date_step values
    select_query = f"""
    SELECT 
        `khronos_data`.`date_step` 
    FROM `khronos_data`
    LEFT JOIN `clients_conf` ON `clients_conf`.`ID` = `khronos_data`.`clientconfid`
    LEFT JOIN `cust_config_web` ON `cust_config_web`.`ID` = `khronos_data`.`clientconfwebid`
    LEFT JOIN `khronos_status` ON `khronos_status`.`ID` = `khronos_data`.`status`
    LEFT JOIN `khronos_scenario_name` ON `khronos_scenario_name`.`id` = `khronos_data`.`scenarioid`
    LEFT JOIN `khronos_scenario_step` ON `khronos_scenario_step`.`step` = khronos_data.stepid 
        AND khronos_scenario_step.scenario = khronos_data.scenarioid
    WHERE `khronos_data`.`date_step` BETWEEN '{start_date}' AND '{end_date}'
      AND `clients_conf`.`CLIENTID` IN (116)
      AND DAYOFWEEK(khronos_data.date_step) IN (2, 3, 4, 5, 6)
      AND EXTRACT(HOUR FROM khronos_data.date_step) IN (8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19)
      AND `khronos_data`.`scenarioid` IN (16, 64)
      AND `khronos_data`.`status` IN (0)
      AND DAYOFMONTH(khronos_data.date_step) NOT IN (0);
    """

    # SQL query to update another table
    update_query_template = """
    UPDATE `khronos_data_containers_v2`
    SET `transaction_status` = 0
    WHERE `scenario_name` LIKE '%VYV%'
      AND `date_step` LIKE '{}';
    """

    try:
        # Establish the database connection
        connection = mysql.connector.connect(**config2)

        if connection.is_connected():
            cursor = connection.cursor(dictionary=True)
            cursor.execute(select_query)
            
            # Fetch all rows from the executed query
            results = cursor.fetchall()
            
            # Display and update based on the retrieved date_step values
            for row in results:
                # Format the date_step field to exclude seconds
                date_step_str = row['date_step'].strftime('%Y-%m-%d %H:%M')
                print(f"Updating with date_step: {date_step_str}")
                
                # Execute the update query
                update_query = update_query_template.format(date_step_str + '%')
                cursor.execute(update_query)
            
            # Commit the updates
            connection.commit()

    except Error as e:
        print(f"Error: {e}")

    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

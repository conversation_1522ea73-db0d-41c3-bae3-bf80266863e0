#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["covea"]["username"], credentials["covea"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-covea.soprahronline.sopra/app/foryou/#/login"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    exit(1)

# Load credentials
username, password = load_credentials()

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    #! transaction_1: Navigate to COVEA application
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Transaction 1: Navigate to COVEA application",
        measure_time=True,
        unique_str="transaction1",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_2: COVEA login process
    check_and_log(
        test.driver,
        lambda: test.covea_direct_login(username, password),
        "Transaction 2: COVEA login process",
        measure_time=True,
        unique_str="transaction2",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_3: Access search functionality
    check_and_log(
        test.driver,
        lambda: test.access_remuneration_functionality(),
        "Transaction 3: Access search functionality",
        measure_time=True,
        unique_str="transaction3",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_4: Search for contract remuneration
    check_and_log(
        test.driver,
        lambda: test.search_for_contract_remuneration(),
        "Transaction 4: Search for contract remuneration",
        measure_time=True,
        unique_str="transaction4",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_5: Select remuneration criteria
    check_and_log(
        test.driver,
        lambda: test.select_remuneration_criteria(),
        "Transaction 5: Select remuneration criteria",
        measure_time=True,
        unique_str="transaction5",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_6: Search employee by matricule
    check_and_log(
        test.driver,
        lambda: test.search_employee_for_remuneration("00410158"),
        "Transaction 6: Search for employee matricule 00410158",
        measure_time=True,
        unique_str="transaction6",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_7: Display and validate remuneration data
    check_and_log(
        test.driver,
        lambda: test.display_and_validate_remuneration_data(),
        "Transaction 7: Display and validate remuneration data",
        measure_time=True,
        unique_str="transaction7",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_8: Logout from application
    check_and_log(
        test.driver,
        lambda: test.covea_logout(),
        "Transaction 8: Logout from COVEA application",
        measure_time=True,
        unique_str="transaction8",
        total_steps=1,
        is_last_step=True,
        take_screenshot=True,
        test_automation_instance=test,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")

finally:
    # Cleanup
    test.teardown_driver()

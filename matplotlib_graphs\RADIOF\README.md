
1. build
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080

cd /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/
podman build -t radiof-automated-cmplx .

2. running in general:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx

3. running for may:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOF - Scénario 1 : Liste de comptes bancaires liés à un collaborateur" "2023-05-01" "2023-05-31" "RADIOF"

4. running for august:

sh run_docker_scenarios.sh

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 1 : Mise en paiement collective des cachetiers et pigistes" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 2 : Coordonnées" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 3 : Gestion des demandes" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 4 : Affectations" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 5 : Temps de travail" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 6 : Transport" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 7 : Primes" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 8 : Données bancaires"  "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 9 : Rémunération" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 10 : Fiche de synthèse" "2023-08-01" "2023-08-31" "RADIOF"










podman run radiof-automated-cmplx


ssh admrpa@frsopslapp065 "docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx


docker run -it radiof-automated-cmplx /bin/bash
podman exec -it  radiof-automated-cmplx /bin/bash

podman run --rm radiof-automated-cmplx



podman save -o radiof-automated-cmplx.tar radiof-automated-cmplx
scp radiof-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/radiof-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913

import matplotlib.backends.backend_pdf
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.table import Table
import sys

# Create some example data
x = np.linspace(0, 10, 100)
y1 = np.sin(x)
y2 = np.cos(x)
y3 = np.tan(x)
y4 = np.exp(-x)

# Accept the output directory as a command-line argument
output_directory = sys.argv[1] if len(sys.argv) > 1 else '.'

# Create a new PDF
pdf = matplotlib.backends.backend_pdf.PdfPages(f'{output_directory}/table_and_grid.pdf')
# Create a new figure for Page 1
fig = plt.figure(figsize=(10, 8))

# Divide the figure into a 1x3 grid
grid_spec = fig.add_gridspec(
    2, 3, height_ratios=[0.1, 1], width_ratios=[0.4, 0.2, 0.4])

# Add a title above the first table
title_ax_left = fig.add_subplot(grid_spec[0, 0])
title_ax_left.set_axis_off()
title_ax_left.text(0.5, 0.5, 'Table 1', fontsize=14, ha='center', va='center')

# Add a table of values on the left side (first subplot)
table_ax_left = fig.add_subplot(grid_spec[1, 0])
data_left = [['X', 'Y'], ['1', '2'], ['3', '4'], [
    '5', '6'], ['7', '8'], ['9', '10'], ['11', '12']]
table_data_left = np.array(data_left)
table_colors_left = [['#ffb057', 'white']] * len(data_left)

table_left = table_ax_left.table(
    cellText=table_data_left,
    cellColours=table_colors_left,
    cellLoc='center'
)
table_left.auto_set_font_size(False)
table_left.set_fontsize(12)
table_left.scale(1.5, 1.5)
table_ax_left.axis('off')

# Add an empty axis in the middle to create space between tables
empty_ax = fig.add_subplot(grid_spec[1, 1])
empty_ax.axis('off')

# Add a title above the second table
title_ax_right = fig.add_subplot(grid_spec[0, 2])
title_ax_right.set_axis_off()
title_ax_right.text(0.5, 0.5, 'Table 2', fontsize=14, ha='center', va='center')

# Add a new table on the right side (third subplot)
table_ax_right = fig.add_subplot(grid_spec[1, 2])
data_right = [['A', 'B'], ['13', '14'], ['15', '16'], [
    '17', '18'], ['19', '20']]
table_data_right = np.array(data_right)
table_colors_right = [['#b0e57c', 'white']] * len(data_right)

table_right = table_ax_right.table(
    cellText=table_data_right,
    cellColours=table_colors_right,
    cellLoc='center'
)
table_right.auto_set_font_size(False)
table_right.set_fontsize(12)
table_right.scale(1.5, 1.5)
table_ax_right.axis('off')

# Adjust layout of the first page
plt.tight_layout(rect=[0, 0.2, 1, 1])

# Save the figure on Page 1
pdf.savefig(fig)
plt.close(fig)

# Create a new figure for Page 2
fig_grid = plt.figure(figsize=(10, 8))

# Create a 2x2 grid of subplots
axes = fig_grid.subplots(nrows=2, ncols=2)

# Add titles above each chart on the second page
axes[0, 0].set_title('Sin(x)')
axes[0, 1].set_title('Cos(x)')
axes[1, 0].set_title('Tan(x)')
axes[1, 1].set_title('Exp(-x)')

# Plot data on each subplot
axes[0, 0].plot(x, y1)
axes[0, 1].plot(x, y2)
axes[1, 0].plot(x, y3)
axes[1, 1].plot(x, y4)

# Adjust layout
plt.tight_layout()

# Save the figure on Page 2
pdf.savefig(fig_grid)
plt.close(fig_grid)

# Close the PDF
pdf.close()

print("Script executed successfully")

#!/bin/bash
# Script: run_docker_scenarios.sh
# Description: This script calculates the start and end dates for the last month
#              and runs a Docker container with those dates for processing.
# Version: 2
# Changes:
# - Fixed a bug where the script executed for the current month instead of the last month.

# Get current year and month
current_year=$(date +%Y)
current_month=$(date +%m)

# Calculate last month's year and month
if [ "$current_month" -eq 1 ]; then
    last_month_year=$((current_year - 1))
    last_month=12
else
    last_month_year=$current_year
    last_month=$((current_month - 1))
fi

# Add leading zero if month is less than 10
if [ "$last_month" -lt 10 ]; then
    last_month="0$last_month"
fi

# Calculate last month's start and end dates
start_date="$last_month_year-$last_month-01"
end_date=$(date -d "$(date -d "$start_date +1 month" +%Y-%m-01) -1 day" "+%Y-%m-%d")

# Run Docker container with calculated dates
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/BGM/pdfs:/app/output bgm-automated-cmplx "PL - scenario PERF BGM-GALERIEE5" "$start_date" "$end_date" "BGM"

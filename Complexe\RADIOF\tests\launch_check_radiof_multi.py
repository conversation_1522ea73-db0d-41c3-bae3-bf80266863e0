# coding=utf-8

import concurrent.futures
import subprocess
from datetime import datetime
# Define scenarios and their corresponding file names
scenarios = [
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc1.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario1.log"),
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc2.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario2.log"),
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc3.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario3.log"),
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc4.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario4.log"),
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc5.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario5.log"),
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc6.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario6.log"),  
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc7.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario7.log"),
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc8.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario8.log"),
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc9.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario9.log"),
    ("/data2/dockerized_rpa_v2/Complexe/RADIOF/sc10.py", "/data2/dockerized_rpa_v2/Complexe/RADIOF/tests/output_scenario10.log")
]

def run_scenario(scenario, output_file):
    command = ["python", scenario]
    with open(output_file, "a") as f:
        f.write("-----------\n")
        timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        f.write("Timestamp: {}\n".format(timestamp))
        subprocess.run(command, stdout=f, stderr=subprocess.STDOUT)

# Run each scenario using ThreadPoolExecutor with max_workers=3
with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
    futures = {executor.submit(run_scenario, scenario, output_file): (scenario, output_file) for scenario, output_file in scenarios}
    for future in concurrent.futures.as_completed(futures):
        scenario, output_file = futures[future]
        try:
            future.result()
        except Exception as e:
            print("Scenario {} encountered an error: {}".format(scenario, e))

print("All scenarios executed successfully.")
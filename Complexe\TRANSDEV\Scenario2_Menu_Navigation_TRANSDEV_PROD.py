#!/usr/bin/env python3
"""
TRANSDEV Scenario 2: Temps et activités Navigation
This scenario logs into TRANSDEV Pleiades and navigates to the "Temps et activités"
(Time and Activities) section, taking screenshots at each step for documentation.
"""
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
    output_failure_results,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["transdev"]["username"], credentials["transdev"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-transdev.soprahronline.sopra/pleiades2/portal/index.jsp"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Load credentials
try:
    username, password = load_credentials()
except Exception as e:
    logging.error(f"Failed to load credentials: {e}")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    #! transaction_1: Navigate to TRANSDEV Pleiades
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Transaction 1: Navigate to TRANSDEV Pleiades",
        measure_time=True,
        unique_str="transaction1",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_2: TRANSDEV login process
    # Step 1: Locate and enter username
    username_field = check_and_log(
        test.driver,
        test.transdev_locate_username_field,
        "Step 1: Locate username field",
        measure_time=True,
        unique_str="transaction2",
        total_steps=3,
    )

    check_and_log(
        test.driver,
        lambda: username_field.send_keys(username),
        "Step 2: Enter username",
        measure_time=True,
        unique_str="transaction2",
    )

    # Step 3: Locate and enter password
    password_field = check_and_log(
        test.driver,
        test.transdev_locate_password_field,
        "Step 3: Locate password field",
        measure_time=True,
        unique_str="transaction2",
    )

    check_and_log(
        test.driver,
        lambda: password_field.send_keys(password),
        "Step 4: Enter password",
        measure_time=True,
        unique_str="transaction2",
    )

    # Step 5: Click login button
    check_and_log(
        test.driver,
        test.transdev_click_login_button,
        "Step 5: Click login button",
        measure_time=True,
        unique_str="transaction2",
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_3: Access main menu and verify dashboard
    check_and_log(
        test.driver,
        test.transdev_access_main_menu,
        "Transaction 3: Access and verify main menu",
        measure_time=True,
        unique_str="transaction3",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_4: Navigate to Temps et activités section
    check_and_log(
        test.driver,
        test.transdev_access_temps_et_activites,
        "Transaction 4: Access Temps et activités section",
        measure_time=True,
        unique_str="transaction4",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_5: Explore Temps et activités functionality
    check_and_log(
        test.driver,
        lambda: True,  # Simple success function
        "Transaction 5: Explore Temps et activités functionality",
        measure_time=True,
        unique_str="transaction5",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_6: Navigate back to main menu
    check_and_log(
        test.driver,
        test.transdev_access_main_menu,
        "Transaction 6: Return to main menu",
        measure_time=True,
        unique_str="transaction6",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_7: Final overview of Temps et activités
    check_and_log(
        test.driver,
        lambda: True,  # Simple success function
        "Transaction 7: Final overview of Temps et activités section",
        measure_time=True,
        unique_str="transaction7",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_8: Logout from TRANSDEV application
    check_and_log(
        test.driver,
        test.transdev_click_exit_button,
        "Transaction 8: Logout from TRANSDEV application",
        measure_time=True,
        unique_str="transaction8",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
        is_last_step=True,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")
    # Output failure results to stdout
    output_failure_results()

finally:
    # Cleanup
    test.teardown_driver()

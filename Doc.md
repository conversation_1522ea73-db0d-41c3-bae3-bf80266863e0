# Dockerized RPA System Documentation

## Overview

The Dockerized RPA (Robotic Process Automation) system is a comprehensive solution designed to automate and monitor performance testing of various client applications. The system consists of two main components:

1. **Complexe**: A containerized Selenium-based automation framework that executes performance test scenarios across multiple client applications.
2. **matplotlib_graphs**: A containerized reporting system that generates performance reports and visualizations based on the data collected by the Complexe component.

The system is designed to run automated tests for various clients (AXA, AXA_SCS_DEV, COVEA, RADIOF, VYV3, GSOS, LCL, TRANSDEV, BGM, CONFOE5, AREAS) and generate performance reports to monitor application health and performance metrics.

## System Architecture

### Components

#### 1. Complexe Component

The Complexe component is responsible for executing automated test scenarios using Selenium WebDriver. It is containerized using Podman/Docker with Oracle Linux 8 as the base image. Key features include:

- **Multi-client Support**: Supports multiple client applications with dedicated test scenarios for each.
- **Parallel Execution**: Uses threading to run multiple test scenarios concurrently.
- **Database Integration**: Records test results in a MySQL database (khronos) for later analysis.
- **Scenario Management**: Each client has specific test scenarios defined in separate Python scripts.
- **Containerization**: Packaged as a Docker/Podman container for consistent execution across environments.
- **Flexible Scheduling**: Supports different execution schedules for clients, including diurnal and nocturnal runs for RADIOF.

#### 2. matplotlib_graphs Component

The matplotlib_graphs component is responsible for generating performance reports and visualizations based on the data collected by the Complexe component. It is containerized using Podman/Docker with Python 3.8 (Debian Buster) as the base image. Key features include:

- **Data Retrieval**: Queries the khronos database to retrieve performance metrics.
- **Visualization**: Uses Matplotlib to generate graphs and charts.
- **PDF Generation**: Creates PDF reports with ReportLab and merges them using PyPDF2.
- **Client-specific Reports**: Generates separate reports for each client.
- **Containerization**: Packaged as a Docker/Podman container for consistent execution across environments.

### Database Structure

The system uses two MySQL databases:

1. **maia**: Contains configuration data for the client applications.
2. **khronos**: Stores test execution results and performance metrics.

Key tables in the khronos database include:
- `khronos_data_containers_v2`: Stores test execution results.
- `khronos_scenario_step`: Defines the steps in each test scenario.
- `khronos_scenario_name`: Maps scenario IDs to scenario names.
- `cust_perf_features`: Defines performance thresholds for each scenario.
- `cust_config_web`: Contains web configuration for each client.
- `clients_conf`: Contains client configuration data.

## Workflow

### Test Execution Workflow

1. The Complexe container is started with a specific client script (e.g., AXA_multi_launch.py).
2. The script queries the database to get scenario information for the specified client.
3. Test scenarios are executed in parallel using threading.
4. Test results (execution time, success/failure status) are recorded in the khronos database.
5. Logs are generated and stored in the Complexe_logs directory.

### Reporting Workflow

1. The matplotlib_graphs container is started for a specific client.
2. The container queries the khronos database to retrieve performance metrics.
3. Graphs and charts are generated using Matplotlib.
4. PDF reports are created using ReportLab.
5. Reports are stored in the pdfs directory for each client.
6. The auto_generate_send_complex_reports.py script can be used to generate reports for all clients.

## Client Applications

The system supports the following client applications:

1. **AXA**: HR and payroll management system for AXA 4YOU production environment.
2. **AXA_SCS_DEV**: Development environment for AXA SCS application.
3. **COVEA**: Employee management system with scenarios for banking, remuneration, and contract management.
4. **RADIOF**: Radio France management system with scenarios for payment processing, coordination, and time management. Includes both diurnal and nocturnal test runs, as well as production and recovery environment testing.
5. **VYV3**: HR management system.
6. **GSOS**: Groupe SOS HR management system.
7. **LCL**: Banking and HR management system.
8. **TRANSDEV**: Transportation company HR management system.
9. **BGM**: HR management system.
10. **CONFOE5**: Configuration management system.
11. **AREAS**: Insurance company HR management system.

Each client has specific test scenarios defined in separate Python scripts, and the system generates client-specific performance reports. Client directories contain scenario scripts that define the test steps and expected behaviors for each application.

## Performance Metrics

The system collects and reports on the following performance metrics:

1. **Availability (SLA)**: Percentage of successful test executions.
2. **Response Time**: Average time taken to complete each test scenario.
3. **Performance (SLA)**: Percentage of test executions that completed within the acceptable time threshold.
4. **Nominal Time**: Target response time for each scenario.
5. **Acceptable Time**: Maximum acceptable response time for each scenario.

These metrics are visualized in daily, weekly, and monthly reports.

## Deployment and Execution

### Building the Containers

#### Complexe Container

```bash
cd /data2/dockerized_rpa_v2/Complexe
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080
export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080
podman build -t seleniumcomplexev2 .
```

#### matplotlib_graphs Container (for each client)

```bash
cd /data2/dockerized_rpa_v2/matplotlib_graphs/[CLIENT]
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080
export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080
podman build -t [client]-automated-cmplx .
```

### Running the Containers

#### Complexe Container

For standard client execution:
```bash
podman run --privileged -e SCRIPTNAME=[CLIENT]_multi_launch.py seleniumcomplexev2
```

For RADIOF with specific time periods:
```bash
# Diurnal execution
podman run --privileged -e SCRIPTNAME=RADIOF_multi_launch_diurne.py seleniumcomplexev2

# Nocturnal execution
podman run --privileged -e SCRIPTNAME=RADIOF_multi_launch_nocturne.py seleniumcomplexev2

# Recovery environment testing
podman run --privileged -e SCRIPTNAME=RADIOF_multi_launch_diurneREC.py seleniumcomplexev2
podman run --privileged -e SCRIPTNAME=RADIOF_multi_launch_nocturneREC.py seleniumcomplexev2
```

#### matplotlib_graphs Container

```bash
podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/[CLIENT]/pdfs:/app/output [client]-automated-cmplx
```

### Automation Scripts

The system includes several shell scripts to automate the execution of tests and generation of reports:

- `run_selenium_podman_[CLIENT].sh`: Runs the Complexe container for a specific client.
- `run_selenium_podman_radiof_diurne.sh`, `run_selenium_podman_radiof_nocturne.sh`: Special scripts for RADIOF time-specific testing.
- `run_docker_scenarios.sh`: Runs the matplotlib_graphs container for a specific client to generate reports.
- `auto_generate_send_complex_reports.py`: Generates reports for all clients.
- `auto_generate_send_complex_reports_dev.py`: Development version with additional debugging features.

## Maintenance and Troubleshooting

### Log Files

- Complexe logs are stored in `/data2/dockerized_rpa_v2/Complexe_logs/`.
- Each client has separate success and failure log files.

### Container Management

```bash
# Clean up resources
podman system prune
podman image prune

# Interactive session
podman exec -it [container-name] /bin/bash

# Container portability
podman save -o [client]-automated-cmplx.tar [client]-automated-cmplx
scp [client]-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/[client]-automated-cmplx.tar
```

### Project Structure

The project is organized as follows:

```
dockerized_rpa_v2/
├── Complexe/                      # Selenium automation component
│   ├── [CLIENT]/                  # Client-specific scenario scripts
│   ├── [CLIENT]_multi_launch.py   # Client-specific launcher scripts
│   ├── Dockerfile                 # Container definition for Selenium
│   ├── entrypoint.sh              # Container entry point script
│   ├── requirements.txt           # Python dependencies
│   └── run_selenium_podman_*.sh   # Execution scripts for each client
│
├── matplotlib_graphs/             # Reporting component
│   ├── [CLIENT]/                  # Client-specific reporting scripts
│   │   ├── Dockerfile             # Client-specific container definition
│   │   ├── get_data.py            # Data retrieval script
│   │   ├── pdfmerge.py            # PDF merging script
│   │   ├── report_graphs.py       # Graph generation script
│   │   ├── report_tables.py       # Table generation script
│   │   └── run_docker_scenarios.sh # Client-specific report generation
│   │
│   ├── auto_generate_send_complex_reports.py # Multi-client report generation
│   └── pdfmerge.py                # Global PDF merging utility
│
└── installation/                  # Installation dependencies
    └── chromium_116.0.5845.180-1_amd64.deb # Browser package
```

## Conclusion

The Dockerized RPA system provides a comprehensive solution for automated performance testing and reporting. By containerizing both the test execution and reporting components, the system ensures consistent execution across environments and simplifies deployment and maintenance. The system's modular design allows for easy addition of new clients and test scenarios, making it a scalable solution for performance monitoring.

## Version History

- **v2.0** (Current): Enhanced with support for multiple client environments, specialized execution modes (diurnal/nocturnal), and improved reporting capabilities.
- **v1.0**: Initial release with basic containerization and support for core clients.

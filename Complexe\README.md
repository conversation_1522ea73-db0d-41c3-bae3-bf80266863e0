# Dockerized RPA v2

This repository contains a containerized Robotic Process Automation (RPA) solution for monitoring and testing various client applications. The system uses Selenium with Python to automate browser interactions and collect performance metrics.

## Overview

The Dockerized RPA v2 system provides:

- Containerized automation for consistent execution across environments
- Centralized monitoring of multiple client applications
- Scheduled execution via cron jobs
- Performance metrics collection and reporting
- Simplified deployment and maintenance

## Project Structure

```
Complexe/
├── Client Directories (containing scenario scripts)
│   ├── AREAS/
│   ├── AXA_4YOU_PROD/
│   ├── AXA_SCS_DEV/
│   ├── BGM/
│   ├── CONFOE5/
│   ├── COVEA_4YOU_PROD/
│   ├── GSOS/
│   ├── LCL/
│   ├── RADIOF/
│   ├── TRANSDEV/
│   └── VYV3/
├── Shell Scripts (for running containers)
│   ├── run_selenium_podman_AREAS.sh
│   ├── run_selenium_podman_axa.sh
│   ├── run_selenium_podman_bgm.sh
│   ├── run_selenium_podman_CONFOE5.sh
│   ├── run_selenium_podman_covea.sh
│   ├── run_selenium_podman_GSOS.sh
│   ├── run_selenium_podman_LCL.sh
│   ├── run_selenium_podman_radiof.sh
│   ├── run_selenium_podman_radiof_diurne.sh
│   ├── run_selenium_podman_radiof_diurneREC.sh
│   ├── run_selenium_podman_radiof_nocturne.sh
│   ├── run_selenium_podman_radiof_nocturneREC.sh
│   ├── run_selenium_podman_TRANSDEV.sh
│   └── run_selenium_podman_vyv3.sh
├── Core Files
│   ├── clone_main_table.py (Database table initialization)
│   ├── Dockerfile (Container definition)
│   ├── entrypoint.sh (Container entry point)
│   ├── multi_launch.py (Consolidated client script)
│   └── requirements.txt (Python dependencies)
└── Support Files
    ├── geckodriver-v0.33.0-linux32.tar.gz (Firefox WebDriver)
    └── get-pip.py (Python package installer)
```

## Consolidated Multi-Launch Script

The system uses a consolidated script (`multi_launch.py`) that can launch scenarios for any client by providing the client name as an environment variable. This replaces the previous approach of having separate scripts for each client.

### Environment Variables

The container accepts the following environment variables:

- `SCRIPTNAME`: The script to execute (typically `multi_launch.py`)
- `CLIENT_NAME`: The client identifier (e.g., `AXA`, `BGM`, `RADIOF`, etc.)

**Note**: The previous format using `-e 2=CLIENT_NAME` is deprecated. Use `-e CLIENT_NAME=CLIENT_NAME` instead.

### Available Clients

The following clients are supported:

- `AXA` - AXA client scenarios
- `AREAS` - AREAS client scenarios
- `BGM` - BGM client scenarios
- `CONFOE5` - CONFOE5 client scenarios
- `COVEA` - COVEA client scenarios
- `GSOS` - GROUPE SOS client scenarios
- `LCL` - LCL client scenarios
- `RADIOF` - Radio France standard scenarios
- `RADIOF` - Radio France diurne scenarios
- `RADIOF` - Radio France nocturne scenarios
- `RADIOF` - Radio France diurne REC scenarios
- `RADIOF` - Radio France nocturne REC scenarios
- `TRANSDEV` - TRANSDEV client scenarios
- `VYV3` - VYV3 client scenarios

## How to Build and Run

### Building the Container

```bash
cd /data2/dockerized_rpa_v2/Complexe
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080
export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080
podman build -t seleniumcomplexev2 .
```

### Running the Container

Interactive mode:
```bash
podman run -it seleniumcomplexev2
```

Running with the consolidated script:
```bash
podman run --privileged \
  -e SCRIPTNAME=multi_launch.py \
  -e CLIENT_NAME=AXA \
  seleniumcomplexev2
```

Examples for all clients:
```bash
# Example for running with a specific client
podman run --privileged \
  -e SCRIPTNAME=multi_launch.py \
  -e CLIENT_NAME=AXA \
  seleniumcomplexev2

# Other client examples
podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=AREAS seleniumcomplexev2
podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=BGM seleniumcomplexev2
podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=CONFOE5 seleniumcomplexev2
podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=COVEA seleniumcomplexev2
podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=GSOS seleniumcomplexev2
podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=LCL seleniumcomplexev2
podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=RADIOF seleniumcomplexev2
podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=TRANSDEV seleniumcomplexev2
podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=VYV3 seleniumcomplexev2
```

### Troubleshooting

If you encounter the error `standard_init_linux.go:225: exec user process caused "no such file or directory"` when running the container, convert the entrypoint script to Unix line endings:

```bash
dos2unix Complexe/entrypoint.sh
```

### Container Maintenance

Clean up unused containers and images:
```bash
podman system prune
podman image prune
```

## Crontab Configuration

Example crontab entries for scheduled execution:

```bash
# Example of direct podman command in crontab (recommended approach)
*/15 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=AREAS seleniumcomplexev2
*/15 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=RADIOF seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=COVEA seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=AXA seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=VYV3 seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=GSOS seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=LCL seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=TRANSDEV seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=BGM seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=CONFOE5 seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=RADIOF_DIURNE seleniumcomplexev2
*/10 20-23,0-7 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=RADIOF_NOCTURNE seleniumcomplexev2
*/10 8-20 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=RADIOF_DIURNE_REC seleniumcomplexev2
*/10 20-23,0-7 * * 1-5 podman run --privileged -e SCRIPTNAME=multi_launch.py -e CLIENT_NAME=RADIOF_NOCTURNE_REC seleniumcomplexev2

# Alternative: Using shell scripts (legacy approach)
# */15 8-20 * * 1-5 sh /data1/Dockerized_RPA/Complexe/run_selenium_podman_AREAS.sh
# */15 8-20 * * 1-5 sh /data1/Dockerized_RPA/Complexe/run_selenium_podman_radiof.sh
```

## How It Works

1. **Container Initialization**:
   - The `entrypoint.sh` script runs when the container starts
   - It first executes `clone_main_table.py` to ensure the database table exists
   - Then it runs the requested script (typically `multi_launch.py`) with the specified client name

2. **Client Script Execution**:
   - The `multi_launch.py` script loads the configuration for the specified client
   - It initializes a headless Firefox browser using Selenium
   - The script navigates to the client's application URL
   - It executes the client-specific scenario scripts
   - Performance metrics are collected during execution

3. **Data Collection and Reporting**:
   - Response times, availability status, and other metrics are recorded
   - Results are stored in the database for monitoring and reporting
   - Any errors or exceptions are logged for troubleshooting

4. **Container Cleanup**:
   - After execution completes, the browser is closed
   - The container exits, and resources are released

## Conclusion

This containerized RPA solution provides a robust, scalable approach to monitoring client applications. By using containers, we ensure consistent execution environments and simplified deployment. The consolidated script approach makes maintenance easier and allows for centralized updates to common functionality.

## Git Configuration

```bash
cd existing_repo
git remote add origin https://gitlab.soprahronline.sopra/monitoring/dockerized_rpa_v2.git
git config --global http.sslVerify false
git config user.email "<EMAIL>"
git config user.name "ma.mahmoud"
git checkout -b devlop origin/devlop
git fetch && git pull
git config --global credential.helper store
```
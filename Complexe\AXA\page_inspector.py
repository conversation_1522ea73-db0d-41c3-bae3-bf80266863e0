#!/usr/bin/env python3
import logging
import sys
import os
import json
import time

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import TestAutomation, check_url_availability, reset_scenario_variables

def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["axa"]["username"], credentials["axa"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise

def save_page_source(driver, step_name):
    """Save the page source to a file."""
    try:
        page_source = driver.page_source
        filename = f"page_source_{step_name}.html"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(page_source)
        logging.info(f"Saved page source for step: {step_name} to {filename}")
    except Exception as e:
        logging.error(f"Failed to save page source for step {step_name}: {e}")

# Set the URL
url = "https://ple5-adp-axa.soprahronline.sopra/app/foryou/#/login"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    exit(1)

# Load credentials
username, password = load_credentials()

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    # Step 1: Navigate to the URL
    test.navigate_to_url()
    save_page_source(test.driver, "step1_navigate_to_url")

    # Step 2: Wait for the page to load
    test.wait_for_page_load()
    save_page_source(test.driver, "step2_wait_for_page_load")

    # Step 3: Locate the shadow host element
    shadow_host = test.locate_shadow_host()
    save_page_source(test.driver, "step3_locate_shadow_host")

    # Step 4: Access the shadow root
    test.shadow_root = test.access_shadow_root(shadow_host)
    save_page_source(test.driver, "step4_access_shadow_root")

    # Step 5: Take screenshot before login
    test.take_screenshot("Step_5_Screenshot_before_login")
    save_page_source(test.driver, "step5_before_login")

    # Step 6: Locate username field
    username_field = test.locate_username_field()
    username_field.send_keys(username)
    save_page_source(test.driver, "step6_enter_username")

    # Step 8: Locate password field
    password_field = test.locate_password_field()
    password_field.send_keys(password)
    save_page_source(test.driver, "step8_enter_password")

    # Step 10: Locate login button
    login_button = test.locate_login_button()
    login_button.click()
    time.sleep(5) # Wait for login to complete
    save_page_source(test.driver, "step11_after_login_click")

    # Step 12: Take screenshot after successful login
    test.take_screenshot("Step_12_Screenshot_after_successful_login")
    save_page_source(test.driver, "step12_after_login")

    # Step 13: Locate burger menu button and click
    burger_menu_button = test.locate_burger_menu_button()
    burger_menu_button.click()
    time.sleep(2)
    save_page_source(test.driver, "step13_after_burger_menu_click")

    # Step 14: Locate and click 'Gestion administrative/Paie'
    gestion_admin_paie_link = test.locate_gestion_administrative_paie_link()
    gestion_admin_paie_link.click()
    time.sleep(2)
    save_page_source(test.driver, "step14_after_gestion_admin_paie_click")

    # Step 15: Locate and click 'Temps et activité'
    temps_activite_link = test.locate_link_text_temps_activite()
    temps_activite_link.click()
    time.sleep(2)
    save_page_source(test.driver, "step15_after_temps_activite_click")

    # Step 16: Locate and click 'Suivi des temps'
    suivi_temps_link = test.locate_link_text_suivi_des_temps()
    suivi_temps_link.click()
    time.sleep(5)
    save_page_source(test.driver, "step16_after_suivi_temps_click")
    
    # Step 17: Locate and click 'AXA France'
    # This is a placeholder, the actual selector needs to be found from the page source
    # For now, just save the page source
    save_page_source(test.driver, "step17_before_clicking_axa_france")


except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")
    # Save page source on error
    save_page_source(test.driver, "error_state")

finally:
    # Cleanup
    test.teardown_driver()

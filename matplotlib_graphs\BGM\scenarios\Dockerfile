FROM oraclelinux:8
# Install system dependencies
RUN dnf install -y gcc-c++ python39-devel firefox wget tar

# # Install geckodriver

# Copy geckodriver tar.gz to the container
COPY geckodriver-v0.33.0-linux32.tar.gz /usr/local/bin/


# Extract the tar.gz file
RUN tar -xf /usr/local/bin/geckodriver-v0.33.0-linux32.tar.gz -C /usr/local/bin/

# Set permissions
RUN chmod +x /usr/local/bin/geckodriver

# Remove the tar.gz file
RUN rm /usr/local/bin/geckodriver-v0.33.0-linux32.tar.gz

# Set up the application
WORKDIR /app
COPY . .

# Install Python dependencies
RUN python3 -m pip install --upgrade pip && pip3 install -r requirements.txt

# Define an entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Set the default command
CMD ["/usr/local/bin/entrypoint.sh"]


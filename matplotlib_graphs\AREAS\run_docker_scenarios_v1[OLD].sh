#!/bin/bash
# Script: run_docker_scenarios_v1[OLD].sh
# Description: This script calculates the start and end dates for the current month
#              and runs a Docker container with those dates for processing.
# Version: 1
# Noticed a bug where the script executed for the current month instead of the last month


# Get current year and month
current_year=$(date +%Y)
current_month=$(date +%m)

# Calculate current month's start and end dates
start_date="$current_year-$current_month-01"
end_date=$(date -d "$(date -d "$start_date +1 month" +%Y-%m-01) -1 day" "+%Y-%m-%d")

# Run Docker containers with calculated dates
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR" "$start_date" "$end_date" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP" "$start_date" "$end_date" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 3 : GTA" "$start_date" "$end_date" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail" "$start_date" "$end_date" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 5 : Portail de services" "$start_date" "$end_date" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités" "$start_date" "$end_date" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel" "$start_date" "$end_date" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique" "$start_date" "$end_date" "AXAE5"

# Tests

## Tester les scénarios sous Complexe

### Statut par client

| Client   | Statut            | Notes                                                                      |
|----------|-------------------|--------------------------------------------------------------------------- |
| **AXA**  | ✅ **OK**         | sc 1 ok mais il faut ajouter les temps d'attente                                                                   
|          | ✅ **OK**         | sc 2 ok mais il faut ajouter les temps d'attente  
|          | ✅ **OK**  **     | sc 3 ok but slow il faut ajouter les temps d'attente                                               
|          | ✅ **OK**         | sc 4 ok but slow il faut ajouter les temps d'attente
|          | 🔄 **EN COURS**   | sc 5 ok but slow il faut ajouter les temps d'attente
| **AREAS**| ✅ **OK**         | scénario modernisé                                                         |
| **CONFOE5** | ✅ **OK**      | scénario modernisé                                                         |
| **BGM**  | ✅ **OK**         | scénario modernisé                                                         |
| **LCL**  | ✅ **OK**         | scénario modernisé                                                         |
| **GSOS** | ✅ **OK**         | scénario modernisé                                                         |
| **TRANSDEV** | ✅ **OK**     | scénario modernisé                                                         |
| **RADIOF** | 🔄 **EN COURS** | x                                                                          |
| **VYV3** | ✅ **OK**         | scénario modernisé                                                         |
| **COVEA** | 🔄 **EN COURS**  | 5 scénarios avec noms français corrects, répertoire modules supprimé - en cours de finalisation |


### Modernisation terminée

Tous les scénarios incluent maintenant :
- 🔐 **Gestion des identifiants JSON**
- 📸 **Captures d'écran avant/après connexion**
- 📝 **Journalisation améliorée avec création automatique de répertoires**
- 🌐 **Vérification de disponibilité des URL**
- 🔄 **Regroupement des transactions et gestion d'erreurs**
- 📋 **Conventions de nommage cohérentes**

### Tâches restantes

- [ ] **Ajouter un mécanisme de retry** aux scénarios pour tous les clients
- [ ] **Exécuter les scénarios en lot** pour les tests
- [ ] **Tester tous les scénarios modernisés** sur tous les clients
- [ ] **Vérifier la résolution du problème de mot de passe BGM**
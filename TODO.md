# Tests

## Tester les scénarios sous Complexe

### Statut par client

| Client   | Statut            | Notes                                                                      |
|----------|-------------------|--------------------------------------------------------------------------- |
| **AXA**  | ✅ **OK**         | sc 1                                                                       |
|          | ✅ **OK**         | sc 2                                                                       |
|          | 🔄 **EN COURS**   | sc 3 ok but slow                                                           |
|          | 🔄 **EN COURS**   | sc 4                                                                       |
| **AREAS**| ✅ **OK**         | scénario modernisé                                                         |
| **CONFOE5** | ✅ **OK**      | scénario modernisé                                                         |
| **BGM**  | ✅ **OK**         | scénario modernisé                                                         |
| **LCL**  | ✅ **OK**         | scénario modernisé                                                         |
| **GSOS** | ✅ **OK**         | scénario modernisé                                                         |
| **TRANSDEV** | ✅ **OK**     | scénario modernisé                                                         |
| **RADIOF** | 🔄 **EN COURS** | x                                                                          |
| **VYV3** | ✅ **OK**         | scénario modernisé                                                         |
| **COVEA** | 🔄 **EN COURS**  | 5 scénarios avec noms français corrects, répertoire modules supprimé - en cours de finalisation |


### Modernisation terminée

Tous les scénarios incluent maintenant :
- 🔐 **Gestion des identifiants JSON**
- 📸 **Captures d'écran avant/après connexion**
- 📝 **Journalisation améliorée avec création automatique de répertoires**
- 🌐 **Vérification de disponibilité des URL**
- 🔄 **Regroupement des transactions et gestion d'erreurs**
- 📋 **Conventions de nommage cohérentes**

### Tâches restantes

- [ ] **Ajouter un mécanisme de retry** aux scénarios pour tous les clients
- [ ] **Exécuter les scénarios en lot** pour les tests
- [ ] **Tester tous les scénarios modernisés** sur tous les clients
- [ ] **Vérifier la résolution du problème de mot de passe BGM**

## 🚀 Nouvelle Priorité Haute

### Script de Test Automatisé pour Validation de Fiabilité des Scénarios
- **Objectif**: Créer un script de test complet pour valider la fiabilité des scénarios clients
- **Fonctionnalités requises**:
  - Script prend uniquement le **nom du client** en paramètre d'entrée
  - Découverte automatique des scénarios disponibles dans le dossier du client
  - Menu interactif pour sélectionner le scénario à tester
  - Exécution du scénario sélectionné **50 fois en parallèle** pour test de charge
  - Journalisation détaillée et calcul du **pourcentage de succès**
  - Génération de rapport complet avec:
    - Total d'exécutions: 50
    - Nombre d'exécutions réussies
    - Nombre d'exécutions échouées
    - Taux de succès en pourcentage
    - Temps d'exécution moyen
    - Analyse des erreurs et patterns d'échec
    - Métriques de performance par exécution
- **Spécifications techniques**:
  - Support d'exécution parallèle (multi-threading/multi-processing)
  - Gestion d'erreurs robuste et journalisation
  - Monitoring en temps réel du progrès
  - Paramètres d'exécution configurables (nombre de runs, timeouts)
  - Export des résultats en CSV/JSON pour analyse
- **Exemple d'usage**: `python test_scenario_reliability.py --client AXA --scenario Scenario1_Connexion_AXA_4YOU_PROD`
- **Priorité**: 🔥 **HAUTE** - Critique pour validation de la robustesse en production
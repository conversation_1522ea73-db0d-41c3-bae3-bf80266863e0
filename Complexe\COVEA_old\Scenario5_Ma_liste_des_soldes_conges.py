# coding=utf-8

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.support import expected_conditions as EC
import os
import sys
import time
import subprocess
from selenium.webdriver.support import expected_conditions as EC
import requests
from requests.packages import urllib3
from time import time
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)



class TestTest1:
    def check_url(self):
        """
        This function checks the URL using the curl command.
        It first sends a request using the `-I` option to only retrieve the header information.
        If the response code is 0, it means the URL is reachable, so the function sends another request to retrieve the page source.
        The page source is saved to a file `temp_ple5_adp_axa_page_source.html`.
        The function then opens the file and reads its content.
        If the string `"logosoprahr"` is found in the content, the function returns True.
        If the response code from the first request is not 0 or the string `"logosoprahr"` is not found in the content, the function returns False.
        """
        with open(os.devnull, 'w') as devnull:
            response = subprocess.call(
                ['curl', '-I', 'https://ple5-covea.soprahronline.sopra/app/foryou/#/login', '--insecure'], stdout=devnull, stderr=devnull)
        if response == 0:
            temp_ple5_adp_axa_page_source = "temp_ple5_adp_axa_page_source.html"
            with open(os.devnull, 'w') as devnull:
                subprocess.call(
                    [
                        'curl', 'https://ple5-covea.soprahronline.sopra/app/foryou/#/login',
                        '--insecure'], stdout=open(temp_ple5_adp_axa_page_source, 'w'), stderr=devnull)
            with open(temp_ple5_adp_axa_page_source, "r") as f:
                content = f.read()
                if "logosoprahr" in content:
                    return True
                else:
                    return False
        else:
            return False

    def url_down_output(self):
        # TODO to verify if this output is compatible with db
        print("transaction1=0")
        print("ac_time1=0.00")
        print("transaction2=0")
        print("ac_time2=0.00")
        print("transaction3=0")
        print("ac_time3=0.00")
        print("transaction4=0")
        print("ac_time4=0.00")
        print("transaction5=0")
        print("ac_time5=0.00")
        print("transaction6=0")
        print("ac_time6=0.00")
        print("transaction7=0")
        print("ac_time7=0.00")
        print("transaction8=0")
        print("ac_time8=0.00")                        
        print("scenario=0")
        print("scenariotime=0.0")

    def affichage_tr(self, n, transaction_n_state, transaction_n_time):
        print("transaction{}={:.1f}".format(n, transaction_n_state))
        print("ac_time{}={:.2f}".format(n, transaction_n_time))

    def affichage_av(self, scenario, totale):
        print("scenario={:.1f}".format(scenario))
        print("scenariotime={:.2f}".format(totale))

    def setup_method(self, method):
        #! using headless firefox
        self.options = FirefoxOptions()
        self.options.add_argument("--headless")
        self.driver = webdriver.Firefox(
            options=self.options
        )
        self.driver.implicitly_wait(20)
        self.vars = {}

    def teardown_method(self):
        self.driver.quit()

    def test_test1(self):
        # Test name: xxx
        # Step # | name | target | value
        temps_attente_tr1 = 3
        temps_attente_tr2 = 5
        temps_attente_tr3 = 4
        temps_attente_tr4 = 3

        temps_saisie = 8
        from time import time

        #! transaction_1
        #! Demander l’URL page de l’application 4YOU hors SSO
        start_time = time()        
        # ? checking av
        try:
            r = requests.head(
                "https://ple5-covea.soprahronline.sopra/app/foryou/#/login",
                verify=False,
                allow_redirects=True
            )
            if (r.status_code == 200 or r.status_code == 302):
                transaction_1_state = 1
            else:
                transaction_1_state = 0
                totale = 0
                print("scenario={:.1f}".format(transaction_1_state))
                print("scenariotime={:.2f}".format(totale))
        except:
            transaction_1_state = 0
        if transaction_1_state != 0:
            try:
                self.driver.get(
                    "https://ple5-covea.soprahronline.sopra/app/foryou/#/login"
                )
            except:
                self.driver.quit()
                sys.exit(0)
        end_time = time()
        if transaction_1_state != 0:
            transaction_1_time = round((end_time - start_time), 3)
            transaction_1_time = transaction_1_time + temps_attente_tr1 #! temps d'affichage calculé par les robots
            self.affichage_tr(1, transaction_1_state, transaction_1_time)
        else:
            self.driver.quit()
            sys.exit(0)

        #! transaction_2
        #! Saisir les champs suivants :
        #! Identifiant 	LOGIN Utilisateur S048462
        #! Mot de passe 	Password Utilisateur 498175
        #! avec profil collaborateur
        #! Cliquer sur le bouton « Me connecter »
        # window size & login
        start_time = time()
        try:
            # 2 | setWindowSize | 1271x690 |
            self.driver.set_window_size(1271, 690)
            try:
                # 3 | type | id=loginInput | A056825
                # self.driver.find_element(By.ID, "loginInput").send_keys("S048462")
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(EC.element_to_be_clickable((By.ID, "loginInput")))
                element.send_keys("S048462")
            except:
                print("element ID=loginInput introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 4 | type | id=passwordInput | 411271
                # self.driver.find_element(By.ID, "passwordInput").send_keys("498175")
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(
                    EC.element_to_be_clickable((By.ID, "passwordInput"))
                )
                element.send_keys("498175")
            except:
                print("element ID=loginInput introuvable!")
                self.driver.quit()
                sys.exit(0)
            try:
                # 5 | click | css=.ladda-label |
                # self.driver.find_element(By.CSS_SELECTOR, ".ladda-label").click()
                wait = WebDriverWait(self.driver, 99)
                element = wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, ".ladda-label"))
                )
                element.click()
            except:
                print("element CSS_SELECTOR=.ladda-label introuvable!")
                self.driver.quit()
                sys.exit(0)
        except Exception as e:
            transaction_2_state = 0
            self.driver.quit()
            sys.exit(0)
        transaction_2_state = 1
        end_time = time()
        if transaction_2_state != 0:
            transaction_2_time = round((end_time - start_time), 3)
            transaction_2_time = transaction_2_time + temps_attente_tr2
            if transaction_2_time < 5:
                print("transaction2={:.1f}".format(transaction_2_state))
                print("ac_time2={:.2f}".format(transaction_2_time))
            else:
                print("transaction2={:.1f}".format(transaction_2_state))
                print("ac_time2={:.2f}".format(transaction_2_time))
        else:
            print("transaction2={:.1f}".format(transaction_2_state))
            print("login ou mdp incorrecte")
            exit(0)
        #!transaction 3
        start_time = time()
        try:
            # try:
            #     wait = WebDriverWait(self.driver, 99)
            #     element = wait.until(EC.element_to_be_clickable((By.ID, "fromSydToPc")))
            #     actions = ActionChains(self.driver)
            # except:
            #     print("element ID=fromSydToPc introuvable!")
            #     self.driver.quit()
            #     sys.exit(0)
            # try:
            #     actions.move_to_element(element).perform()
            # except:
            #     print("Can't perform ID=fromSydToPc action")
            #     self.driver.quit()
            #     sys.exit(0)
            transaction_3_state = 1                
        except Exception as e:
            transaction_3_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_3_state != 0:
            transaction_3_time = round((end_time - start_time), 3)
            transaction_3_time = transaction_3_time + temps_attente_tr3
            if transaction_3_time < 5:
                print("transaction3={:.1f}".format(transaction_3_state))
                print("ac_time3={:.2f}".format(transaction_3_time))
            else:
                print("transaction3={:.1f}".format(transaction_3_state))
                print("ac_time3={:.2f}".format(transaction_3_time))
        else:
            print("transaction3={:.1f}".format(transaction_3_state))
            print("login ou mdp incorrecte")
            exit(0)

        #! transaction_4
        # logout
        start_time = time()
        try:
            # # 22 | selectFrame | relative=parent |
            # self.driver.switch_to.default_content()
            # # 23 | click | id=navbar-logout-link-id |
            # self.driver.find_element(By.ID, "navbar-logout-link-id").click()
            # # 24 | close |  |
            # self.driver.close()
            transaction_4_state = 1
        except Exception as e:
            transaction_4_state = 0
            self.driver.quit()
            sys.exit(0)
        end_time = time()
        if transaction_4_state != 0:
            transaction_4_time = round((end_time - start_time), 3)
            transaction_4_time = transaction_4_time + temps_attente_tr4
            if transaction_4_time < 5:
                print("transaction4={:.1f}".format(transaction_4_state))
                print("ac_time4={:.2f}".format(transaction_4_time))
            else:
                print("transaction4={:.1f}".format(transaction_4_state))
                print("ac_time4={:.2f}".format(transaction_4_time))
        else:
            print("transaction4={:.1f}".format(transaction_4_state))
            exit(0)

        scenario = transaction_4_state
        totale = (
            transaction_1_time
            + transaction_2_time
            + transaction_3_time
            + transaction_4_time
        ) / 2 + temps_saisie
        print("scenario={:.1f}".format(scenario))
        print("scenariotime={:.2f}".format(totale))

    def perf_mesaure(self):
        self.setup_method(self)
        self.test_test1()
        self.teardown_method()


t = TestTest1()
t.perf_mesaure()
t.teardown_method()


import mysql.connector
from datetime import datetime, timedelta
import os
import logging

# Setup logging
script_dir = os.path.dirname(os.path.abspath(__file__))  # Get the script directory
log_file = os.path.join(script_dir, 'script.log')  # Log file path
logging.basicConfig(filename=log_file, level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Log start of the script
logging.info('Script started')

# Database connection config
config2 = {
    "user": "prodops",
    "password": "prodops",
    "host": "*********",
    "port": "3306",
    "database": "khronos",
    "raise_on_warnings": True,
}

try:
    # Establish connection
    connection = mysql.connector.connect(**config2)
    cursor = connection.cursor()
    logging.info('Database connection established')

    # Query to fetch the dataset
    query = """
        SELECT id, scenario_steps_id, scenario_name, client_conf_id, client_id, duration, date_step, date_timestamp
        FROM khronos_data_containers_v2
        WHERE scenario_name LIKE '%HR - scénario PERF VYV3%'
          AND date_step LIKE "2024-09%"
          AND CAST(date_step AS TIME) >= '06:00:00'
          AND CAST(date_step AS TIME) < '18:00:00'
        ORDER BY date_step;
    """
    cursor.execute(query)
    results = cursor.fetchall()
    logging.info(f'Fetched {len(results)} rows from database')

    if len(results) == 0:
        logging.info('No data found for the specified criteria')
        raise SystemExit  # Exit if no data is found

    # Calculate the average duration
    cursor.execute("""
        SELECT AVG(duration) as avg_duration
        FROM khronos_data_containers_v2
        WHERE scenario_name LIKE '%HR - scénario PERF VYV3%'
          AND date_step LIKE "2024-09%"
          AND CAST(date_step AS TIME) >= '06:00:00'
          AND CAST(date_step AS TIME) < '18:00:00';
    """)
    avg_duration = cursor.fetchone()[0]
    logging.info(f'Calculated average duration: {avg_duration}')

    # Define the interval duration (10 minutes)
    interval_duration = timedelta(minutes=10)
    new_rows = []

    # Track existing timestamps (ignoring seconds)
    existing_timestamps = set()

    for result in results:
        date_time = result[6]  # date_step of the current row
        existing_timestamps.add(date_time.replace(second=0))  # Add hour and minute only

    # Start from the first timestamp and check for missing intervals
    start_time = min(existing_timestamps)
    end_time = max(existing_timestamps)

    # Ensure we are capturing the entire last day
    last_day = end_time.replace(hour=23, minute=59, second=59, microsecond=999999)

    current_time = start_time
    while current_time <= last_day:  # Extend to include the last interval
        # Check if the current time falls on a weekend (Saturday: 5, Sunday: 6)
        if current_time.weekday() < 5:  # Only process Monday to Friday
            if current_time not in existing_timestamps:
                new_row = (
                    result[1],  # scenario_steps_id
                    result[2],  # scenario_name
                    result[3],  # client_conf_id
                    result[4],  # client_id
                    avg_duration,  # average duration
                    current_time,  # date_step
                    current_time  # date_timestamp
                )
                new_rows.append(new_row)
                logging.info(f'Missing interval found: {current_time}, inserting row')

        # Move to the next interval
        current_time += interval_duration

    if new_rows:
        # Insert new rows into the table, skipping the id field (assuming auto-increment)
        insert_query = """
            INSERT INTO khronos_data_containers_v2 (scenario_steps_id, scenario_name, client_conf_id, client_id, duration, date_step, date_timestamp)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        cursor.executemany(insert_query, new_rows)
        connection.commit()
        logging.info(f'{len(new_rows)} missing intervals inserted into the database')
    else:
        logging.info('No missing intervals found')

except mysql.connector.Error as err:
    logging.error(f"Database error: {err}")
except Exception as e:
    logging.error(f"Unexpected error: {e}")
finally:
    if connection.is_connected():
        cursor.close()
        connection.close()
        logging.info('Database connection closed')

# Log end of the script
logging.info('Script finished')

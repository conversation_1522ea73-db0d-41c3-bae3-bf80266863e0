#!/bin/bash
# Script: run_docker_scenarios_v1[OLD].sh
# Description: This script calculates the start and end dates for the current month
#              and runs a Docker container with those dates for processing.
# Version: 1
# Noticed a bug where the script executed for the current month instead of the last month


# Get current year and month
current_year=$(date +%Y)
current_month=$(date +%m)

# Calculate current month's start and end dates
start_date="$current_year-$current_month-01"
end_date=$(date -d "$(date -d "$start_date +1 month" +%Y-%m-01) -1 day" "+%Y-%m-%d")

# Run Docker container with calculated dates
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/LCL/pdfs:/app/output lcl-automated-cmplx "HR - scénario PERF LCL" "$start_date" "$end_date" "LCL"

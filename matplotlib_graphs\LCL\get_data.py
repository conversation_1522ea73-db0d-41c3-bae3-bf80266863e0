import mysql.connector

#! test LCL
config2 = {
    "user": "prodops",
    "password": "prodops",
    "host": "*********",
    "port": "3306",
    "database": "khronos",
    "raise_on_warnings": True,
}


def daily_graph_data(scenario, start_date, end_date):
    connection = mysql.connector.connect(**config2)
    cursor = connection.cursor()
    sql_query = """
            SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE %s   
                AND kdc.date_step BETWEEN %s AND %s
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
                
    """
    # Pass the scenario as a parameter to execute    
    cursor.execute(sql_query, (scenario, start_date, end_date))  
    records = cursor.fetchall()
    query_with_params = cursor.statement
    print("daily_graph_data Query:", query_with_params)    
    cursor.close()
    connection.close()
    return records

# def daily_graph_data():
#     connection = mysql.connector.connect(**config2)
#     cursor = connection.cursor()
#     sql_query = """
#             SELECT
#                 DATE(kdc.date_step) AS per_day,
#                 cpf.NOMINALTIME,
#                 cpf.ACCEPTABLETIME,
#                 SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
#                 SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
#                 SUM(CASE WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 and cpf.ACCEPTABLETIME THEN 1 ELSE 0 END) AS nbupslatime,
#                 SUM(CASE WHEN kdc.transaction_status = 1 AND kdc.duration >= 0 THEN 1 ELSE 0 END) AS nballslatime,
#                 ROUND(AVG(kdc.duration), 2) AS moyrestime
#             FROM
#                 khronos_data_containers_v2 kdc,
#                 cust_perf_features cpf
#             WHERE
#                 kdc.scenario_name LIKE 'PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur'
#                 AND kdc.date_step BETWEEN '2023-05-01' AND '2023-05-31'
#                 and cpf.SCENARIOID = kdc.scenario_id
#             GROUP BY
#                 DATE(kdc.date_step);
#     """
#     cursor.execute(sql_query)
#     records = cursor.fetchall()
#     cursor.close()
#     connection.close()
#     return records


def weekly_graph_data(scenario, start_date, end_date):
    connection = mysql.connector.connect(**config2)
    cursor = connection.cursor()
    sql_query = """
                SELECT 
                    WEEK(kdc.date_step) AS week_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE %s   
                    AND kdc.date_step BETWEEN %s AND %s
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                WEEK(kdc.date_step)
    """
    # Pass the scenario as a parameter to execute    
    cursor.execute(sql_query, (scenario, start_date, end_date))  
    records = cursor.fetchall()
    cursor.close()
    connection.close()
    return records

# def weekly_graph_data():
#     connection = mysql.connector.connect(**config2)
#     cursor = connection.cursor()
#     sql_query = """
#                 SELECT 
#                     WEEK(kdc.date_step) AS week_number,
#                     cpf.NOMINALTIME, 
#                     cpf.ACCEPTABLETIME,
#                     SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
#                     SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
#                     SUM(CASE WHEN kdc.transaction_status = 1 AND kdc.duration
#                     BETWEEN 0 and cpf.ACCEPTABLETIME THEN 1 ELSE 0 END) AS nbupslatime,
#                     SUM(CASE WHEN kdc.transaction_status = 1 AND kdc.duration >= 0 THEN 1 ELSE 0 END) AS nballslatime,
#                     ROUND(AVG(kdc.duration), 2) AS moyrestime 
#                 FROM 
#                     khronos_data_containers_v2 kdc,
#                     cust_perf_features cpf 
#                 WHERE kdc.scenario_name LIKE 'PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat' 
#                     AND kdc.date_step BETWEEN '2023-05-01' AND '2023-05-31' 
#                     and cpf.SCENARIOID = kdc.scenario_id 
#                     GROUP BY WEEK(kdc.date_step)
#     """
#     cursor.execute(sql_query)
#     records = cursor.fetchall()
#     cursor.close()
#     connection.close()
#     return records

def calculate_percentage(nbupslaav, totalTransactions):
    if totalTransactions > 0:
        return (nbupslaav / totalTransactions) * 100
    else:
        return 0
    
def month_av_temps_moyen_percentage(scenario, start_date, end_date):
    connection = mysql.connector.connect(**config2)
    cursor = connection.cursor()
    sql_query = ""
    sql_query = """
                SELECT ROUND(AVG(total_duration), 2) AS temps_moyen
                    FROM (
                        SELECT
                            MIN(scenario_steps_id) AS scenario_steps_id,
                            scenario_name,
                            MIN(scenario_step_name) AS scenario_step_name,
                            SUM(duration) AS total_duration,
                            date_step
                        FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                        WHERE kdc.scenario_name LIKE %s 
                        AND kdc.date_step BETWEEN %s AND  %s
                        GROUP BY date_step
                    ) AS subquery
                """
    # Pass the scenario as a parameter to execute                    
    cursor.execute(sql_query, (scenario, start_date, end_date))  
    records = cursor.fetchall()
    temps_moyen = records[-1][0]
    query_with_params = cursor.statement    
    print("month_av_temps_moyen_percentage Query:", query_with_params)        
    cursor.close()
    connection.close()
    return temps_moyen

def month_av_perf_percentage(scenario, start_date, end_date):
    connection = mysql.connector.connect(**config2)
    cursor = connection.cursor()
    sql_query = ""
    sql_query = """
                SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE %s   
                    AND kdc.date_step BETWEEN %s AND %s
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
                """
    # Pass the scenario as a parameter to execute                    
    cursor.execute(sql_query, (scenario, start_date, end_date))  
    records = cursor.fetchall()
    query_with_params = cursor.statement    
    print("month_av_perf_percentage Query:", query_with_params)        
    cursor.close()
    connection.close()

    nbupslaav_values = [record[3] for record in records]
    nballslaav_values = [record[4] for record in records]

    nbupslatime_values = [record[5] for record in records]
    nballslatime_values = [record[6] for record in records]
    # Calculate the sum of values
    sum_nbupslaav = sum(nbupslaav_values)
    sum_nballslaav = sum(nballslaav_values)
    sum_nbupslatime = sum(nbupslatime_values)
    sum_nballslatime = sum(nballslatime_values)
    av_perc = sum_nbupslaav / sum_nballslaav * 100
    perf_perc = sum_nbupslatime / sum_nballslatime * 100
    perf_perc = round(perf_perc, 2)
    av_perc = round(av_perc, 2)
    return av_perc, perf_perc

# def month_av_perf_percentage():
#     connection = mysql.connector.connect(**config2)
#     cursor = connection.cursor()
#     sql_query = ""
#     sql_query = """
#                 SELECT
#                     MONTH(kdc.date_step) AS month_number,
#                     cpf.NOMINALTIME,
#                     cpf.ACCEPTABLETIME,
#                     SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
#                     SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
#                     SUM(CASE WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 and cpf.ACCEPTABLETIME THEN 1 ELSE 0 END) AS nbupslatime,
#                     SUM(CASE WHEN kdc.transaction_status = 1 AND kdc.duration >= 0 THEN 1 ELSE 0 END) AS nballslatime,
#                     ROUND(AVG(kdc.duration), 2) AS moyrestime
#                 FROM
#                     khronos_data_containers_v2 kdc,
#                     cust_perf_features cpf
#                 WHERE
#                     kdc.scenario_name LIKE 'PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur'
#                     AND kdc.date_step BETWEEN '2023-05-01' AND '2023-05-31'
#                     and cpf.SCENARIOID = kdc.scenario_id
#                 GROUP BY
#                     MONTH(kdc.date_step)
#                 """
#     cursor.execute(sql_query)
#     records = cursor.fetchall()
#     cursor.close()
#     connection.close()

#     nbupslaav_values = [record[3] for record in records]
#     nballslaav_values = [record[4] for record in records]

#     nbupslatime_values = [record[5] for record in records]
#     nballslatime_values = [record[6] for record in records]
#     # Calculate the sum of values
#     sum_nbupslaav = sum(nbupslaav_values)
#     sum_nballslaav = sum(nballslaav_values)
#     sum_nbupslatime = sum(nbupslatime_values)
#     sum_nballslatime = sum(nballslatime_values)
#     av_perc = sum_nbupslaav / sum_nballslaav * 100
#     perf_perc = sum_nbupslatime / sum_nballslatime * 100
#     perf_perc = round(perf_perc, 2)
#     av_perc = round(av_perc, 2)
#     return av_perc, perf_perc


def temps_nominale(client_name, scenario):
    connection = mysql.connector.connect(**config2)
    cursor = connection.cursor()

    sql_query = ""

    if client_name == "COVEA":
        sql_query = """
            SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE %s
                    AND cc1.CUSTCONFLABEL LIKE %s
                    AND ksn.name like %s
                    AND ccw1.CONTEXT_ROOT_ACC LIKE %s
                    AND ksn.name like %s
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE %s
                    AND cc2.CUSTCONFLABEL LIKE %s
                    AND ccw2.CONTEXT_ROOT_ACC LIKE %s
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
        """
        query_params = (
            f'%{client_name}%', f'{client_name}-PROD', scenario,
            '%/app/foryou/#/login%', scenario,
            f'%{client_name}%', f'{client_name}-PROD', '%/app/foryou/#/login%'
        )
    elif client_name == "AXAE5":
        sql_query = """
        
            SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE %s
                AND cc1.CUSTCONFLABEL LIKE %s
                AND ksn.name like %s
                AND ccw1.CONTEXT_ROOT_ACC LIKE %s
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE %s
                AND cc2.CUSTCONFLABEL LIKE %s
                AND ccw2.CONTEXT_ROOT_ACC LIKE %s
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
        """
        query_params = (
            f'%{client_name}%', f'{client_name}-PROD', scenario,
            '%/app/foryou/%',
            f'%{client_name}%', f'{client_name}-PROD', '%/app/foryou/%'
        )
    elif client_name == "RADIOF":
        sql_query = """
            SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
            FROM khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
                JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE %s
                    AND cc1.CUSTCONFLABEL LIKE %s
                    AND ccw1.CONTEXT_ROOT_ACC LIKE %s
                    AND ksn.name like %s
                JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE %s
                    AND cc2.CUSTCONFLABEL LIKE %s
                    AND ccw2.CONTEXT_ROOT_ACC LIKE %s
            GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
        """
        query_params = (
            f'%{client_name}%', f'{client_name}-PROD',
            '%pleiades/portal/index.jsp%', scenario,
            f'%{client_name}%', f'{client_name}-PROD', '%pleiades/portal/index.jsp%'
        )
    elif client_name == "LCL":
        sql_query = """
            SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%LCL%'
                AND cc1.CUSTCONFLABEL LIKE 'LCL-PROD'
                AND ksn.name like 'HR - scénario PERF LCL'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%hra-space/?nosso%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%LCL%'
                AND cc2.CUSTCONFLABEL LIKE 'LCL-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%hra-space/?nosso%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
        """ 
        cursor.execute(sql_query)
        records = cursor.fetchall()
        query_with_params = cursor.statement
        print("Executed Query:", query_with_params)

        cursor.close()
        connection.close()
        print("records:\n", records)
        temps_reponse_nominal = records[-1][-1]
        print("temps_reponse_nominal:\n", temps_reponse_nominal)
        return temps_reponse_nominal              
    cursor.execute(sql_query, query_params)
    records = cursor.fetchall()
    query_with_params = cursor.statement
    print("Executed Query:", query_with_params)

    cursor.close()
    connection.close()
    print("records:\n", records)
    temps_reponse_nominal = records[-1][-1]
    print("temps_reponse_nominal:\n", temps_reponse_nominal)
    return temps_reponse_nominal


# def temps_nominale(client_name, selected_scenario):
#     connection = mysql.connector.connect(**config2)
#     cursor = connection.cursor()

#     sql_query = ""

#     if client_name == "COVEA":
#         sql_query = """
#             SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
#             FROM khronos_scenario_step kss
#                 JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
#                 JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
#                 JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
#                 JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
#                     AND ccw1.CLIENTCONFID = cc1.ID
#                     AND cc1.CLIENTNAME LIKE %s
#                     AND cc1.CUSTCONFLABEL LIKE %s
#                     AND ksn.name like %s
#                     AND ccw1.CONTEXT_ROOT_ACC LIKE %s
#                     AND ksn.name like %s
#                 JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
#                 JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
#                     AND ccw2.CLIENTCONFID = cc2.ID
#                     AND cc2.CLIENTNAME LIKE %s
#                     AND cc2.CUSTCONFLABEL LIKE %s
#                     AND ccw2.CONTEXT_ROOT_ACC LIKE %s
#             GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
#         """
#         query_params = (
#             f'%{client_name}%', f'{client_name}-PROD', selected_scenario,
#             '%/app/foryou/#/login%', selected_scenario,
#             f'%{client_name}%', f'{client_name}-PROD', '%/app/foryou/#/login%'
#         )
#     elif client_name == "AXAE5":
#         sql_query = """
#             SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
#             FROM khronos_scenario_step kss
#                 JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
#                 JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
#                 JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
#                 JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
#                     AND ccw1.CLIENTCONFID = cc1.ID
#                     AND cc1.CLIENTNAME LIKE %s
#                     AND cc1.CUSTCONFLABEL LIKE %s
#                     AND ksn.name like %s
#                     AND ccw1.CONTEXT_ROOT_ACC LIKE %s
#                 JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
#                 JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
#                     AND ccw2.CLIENTCONFID = cc2.ID
#                     AND cc2.CLIENTNAME LIKE %s
#                     AND cc2.CUSTCONFLABEL LIKE %s
#                     AND ccw2.CONTEXT_ROOT_ACC LIKE %s
#             GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
#         """
#         query_params = (
#             f'%{client_name}%', f'{client_name}-PROD', selected_scenario,
#             '%/app/foryou/%',
#             f'%{client_name}%', f'{client_name}-PROD', '%/app/foryou/%'
#         )
#     elif client_name == "RADIOF":
#         sql_query = """
#             SELECT cpf.NOMINALTIME, cpf.ACCEPTABLETIME
#             FROM khronos_scenario_step kss
#                 JOIN khronos_scenario_name ksn ON kss.scenario = ksn.id
#                 JOIN cust_perf_features cpf ON ksn.id = cpf.SCENARIOID
#                 JOIN cust_config_web ccw1 ON cpf.CUSTCONFWEBID = ccw1.CLIENTID
#                 JOIN clients_conf cc1 ON ccw1.CLIENTID = cc1.CLIENTID
#                     AND ccw1.CLIENTCONFID = cc1.ID
#                     AND cc1.CLIENTNAME LIKE %s
#                     AND cc1.CUSTCONFLABEL LIKE %s
#                     AND ccw1.CONTEXT_ROOT_ACC LIKE %s
#                     AND ksn.name like %s
#                 JOIN cust_config_web ccw2 ON cpf.CUSTCONFID = ccw2.CLIENTCONFID
#                 JOIN clients_conf cc2 ON ccw2.CLIENTID = cc2.CLIENTID
#                     AND ccw2.CLIENTCONFID = cc2.ID
#                     AND cc2.CLIENTNAME LIKE %s
#                     AND cc2.CUSTCONFLABEL LIKE %s
#                     AND ccw2.CONTEXT_ROOT_ACC LIKE %s
#             GROUP BY cpf.NOMINALTIME, cpf.SCENARIOID
#         """
#         query_params = (
#             f'%{client_name}%', f'{client_name}-PROD',
#             '%pleiades/portal/index.jsp%', selected_scenario,
#             f'%{client_name}%', f'{client_name}-PROD', '%pleiades/portal/index.jsp%'
#         )
#     cursor.execute(sql_query, query_params)
#     records = cursor.fetchall()
#     query_with_params = cursor.statement
#     # print("Executed Query:", query_with_params)

#     cursor.close()
#     connection.close()
#     temps_reponse_nominal = records[-1][-1]
#     return temps_reponse_nominal

# client_name = "COVEA"  # Replace with the desired client name
# # Replace with the desired scenario value
# selected_scenario = "PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur"

# results = temps_nominale(client_name, selected_scenario)

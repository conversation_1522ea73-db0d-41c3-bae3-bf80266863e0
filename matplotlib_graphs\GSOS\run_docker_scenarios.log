daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'HR - scénario PERF GSOS'   
                AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME
                FROM
                    khronos_scenario_step kss
                JOIN khronos_scenario_name ksn ON
                    kss.scenario = ksn.id
                JOIN cust_perf_features cpf ON
                    ksn.id = cpf.SCENARIOID
                JOIN cust_config_web ccw1 ON
                    cpf.CUSTCONFWEBID = ccw1.ID
                JOIN clients_conf cc1 ON
                    ccw1.CLIENTID = cc1.CLIENTID
                    AND ccw1.CLIENTCONFID = cc1.ID
                    AND cc1.CLIENTNAME LIKE '%GROUPE SOS%'
                    AND cc1.CUSTCONFLABEL LIKE 'GROUPE SOS-PROD'
                    AND ksn.name like 'HR - scénario PERF GSOS'
                    AND ccw1.CONTEXT_ROOT_ACC LIKE '%hra-space/portal%'
                JOIN cust_config_web ccw2 ON
                    cpf.CUSTCONFID = ccw2.CLIENTCONFID
                JOIN clients_conf cc2 ON
                    ccw2.CLIENTID = cc2.CLIENTID
                    AND ccw2.CLIENTCONFID = cc2.ID
                    AND cc2.CLIENTNAME LIKE '%GROUPE SOS%'
                    AND cc2.CUSTCONFLABEL LIKE 'GROUPE SOS-PROD'
                    AND ccw2.CONTEXT_ROOT_ACC LIKE '%hra-space/portal%'
                GROUP BY
                    cpf.NOMINALTIME,
                    cpf.SCENARIOID
records:
 [(-1, -1)]
temps_reponse_nominal:
 -1
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'HR - scénario PERF GSOS'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'HR - scénario PERF GSOS'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
Current Directory: /app
selected scenario:
 HR - scénario PERF GSOS
selected start_date:
 2024-03-01
selected end_date:
 2024-03-31
selected client_name:
 GSOS
running report_graphs.py
running report_tables.py

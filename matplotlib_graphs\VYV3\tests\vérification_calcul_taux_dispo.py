import mysql.connector
from datetime import datetime

#! test VYV
config2 = {
    "user": "prodops",
    "password": "prodops",
    "host": "*********",
    "port": "3306",
    "database": "khronos",
    "raise_on_warnings": True,
}

def month_av_perf_percentage(scenario, start_date, end_date):
    # Strip year-month from start_date and end_date
    start_year_month = datetime.strptime(start_date, "%Y-%m-%d").strftime("%Y-%m")
    end_year_month = datetime.strptime(end_date, "%Y-%m-%d").strftime("%Y-%m")

    connection = mysql.connector.connect(**config2)
    cursor = connection.cursor()

    sql_query = """        
            SELECT
                MONTH(kdc.date_step) AS month_number,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                    CASE
                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                        WHEN kdc.transaction_status = 1 THEN 1
                        ELSE 0
                    END
                ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration), 2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE %s   
                AND kdc.date_step LIKE %s
                AND CAST(kdc.date_step AS TIME) >= '06:00:00'
                AND CAST(kdc.date_step AS TIME) < '18:00:00'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                MONTH(kdc.date_step)
    """
                
    # Pass the scenario and year_month as parameters to execute                    
    cursor.execute(sql_query, (scenario, f"%{start_year_month}%"))  # Like '%YYYY-MM%'
    records = cursor.fetchall()
    query_with_params = cursor.statement    
    print("month_av_perf_percentage Query:", query_with_params)        
    cursor.close()
    connection.close()

    # Process the results
    nbupslaav_values = [record[3] for record in records]
    print("nbupslaav_values:", nbupslaav_values)
    nballslaav_values = [record[4] for record in records]
    print("nballslaav_values:", nballslaav_values)

    nbupslatime_values = [record[5] for record in records]
    print("nbupslatime_values:", nbupslatime_values)

    nballslatime_values = [record[6] for record in records]
    print("nballslatime_values:", nballslatime_values)
    
    # Calculate the sum of values
    sum_nbupslaav = sum(nbupslaav_values)
    print("sum_nbupslaav:", sum_nbupslaav)
    sum_nballslaav = sum(nballslaav_values)
    print("sum_nballslaav:", sum_nballslaav)    
    sum_nbupslatime = sum(nbupslatime_values)
    print("sum_nbupslatime:", sum_nbupslatime)    
    sum_nballslatime = sum(nballslatime_values)
    print("sum_nballslatime:", sum_nballslatime)    

    # Calculate availability and performance percentages
    av_perc = sum_nbupslaav / sum_nballslaav * 100
    print("av_perc:", av_perc)        
    perf_perc = sum_nbupslatime / sum_nballslatime * 100
    print("perf_perc:", perf_perc)        

    # Round the percentages
    perf_perc = round(perf_perc, 2)
    av_perc = round(av_perc, 2)

    return av_perc, perf_perc

# Use sys.argv to accept command-line arguments
import sys

scenario = sys.argv[1]
start_date = sys.argv[2]
end_date = sys.argv[3]

# Call the function and pass the scenario, start_date, and end_date
month_av_perf_percentage(scenario, start_date, end_date)

# Usage Example:
# python3 vérification_calcul_taux_dispo.py "HR - scénario PERF VYV3" "2024-09-01" "2024-09-30"

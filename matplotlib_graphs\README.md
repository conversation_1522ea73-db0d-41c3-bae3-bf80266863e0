# Matplotlib Graphs

A containerized application for generating and merging PDF reports with graphs and visualizations using Python and Matplotlib.

## Overview

The matplotlib_graphs component is designed to create data visualizations and PDF reports. It uses a Python-based container with Matplotlib, NumPy, and ReportLab to generate PDF documents with graphs and merged reports.

## Features

- PDF generation with Matplotlib visualizations
- PDF merging capabilities (using pdfmerge.py)
- Containerized application for consistent environments
- Volume mounting for persistent output storage

## Requirements

- Docker or Podman
- Network access for proxy configuration (if needed)

## Installation & Setup

### Setting up the environment

If you're behind a proxy, set the environment variables:

```bash
# For Docker/Podman environment
ENV http_proxy=http://frsopslinfv01.soprahronline.sopra:8080
ENV https_proxy=http://frsopslinfv01.soprahronline.sopra:8080

# For host environment
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080
export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080
```

### Building the container

```bash
podman build -t matplotlib-app .
# or using Docker
# docker build -t matplotlib-app .
```

## Usage

### Running the container

Standard execution:
```bash
podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/pdfs:/app/output matplotlib-app
```

Remote execution:
```bash
ssh admrpa@frsopslapp065 "docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/pdfs:/app/output matplotlib-app"
```

### Interactive session

```bash
docker run -it matplotlib-app /bin/bash
# or with Podman
# podman exec -it matplotlib-app /bin/bash
```

### Clean up resources

```bash
podman system prune
podman image prune
```

## Container Portability

Export and import the container:

```bash
# Save container as tar
podman save -o matplotlib-app.tar matplotlib-app

# Transfer to another server
scp matplotlib-app.tar admjenkins@frsopslappv55:/tmp

# Load container on target server
docker load -i /tmp/matplotlib-app.tar

# Check available images
docker images

# Run the container
docker run -v /tmp:/app/output e79df6561913
```

## Additional Information

The application uses:
- Python 3.8 (Debian Buster base)
- Matplotlib 3.3 for visualization
- NumPy 1.15.0 for data processing
- ReportLab for PDF generation
- PyPDF2 for PDF manipulation

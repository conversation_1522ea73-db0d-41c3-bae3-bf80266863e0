import matplotlib.backends.backend_pdf
import matplotlib.pyplot as plt
import numpy as np
from get_data import weekly_graph_data

# Get the weekly data
weekly_data = weekly_graph_data()

# Extract the weekly values
week_numbers = [weekly_data[i][0] for i in range(len(weekly_data))]
# print("week numbers: {}".format(week_numbers))
av_percentage_values = [
    float(weekly_data[i][3]) / float(weekly_data[i][4]) * 100
    for i in range(len(weekly_data))
]
# print("percentage_values: {}".format(percentage_values))

perf_percentage_values = [
    round(float(weekly_data[i][5]) / float(weekly_data[i][6]) * 100, 2)
    for i in range(len(weekly_data))
]

# print("perf_percentage_values:", perf_percentage_values)

weekly_graph_plot_data_perf = {
    week_numbers[i]: perf_percentage_values[i]
    for i in range(len(week_numbers))
}
print("weekly_graph_plot_data_perf: {}".format(weekly_graph_plot_data_perf))

weekly_graph_plot_data_av = {
    week_numbers[i]: av_percentage_values[i]
    for i in range(len(week_numbers))
}
# print("weekly_graph_plot_data_av: {}".format(weekly_graph_plot_data_av))

# Create some example data
x = np.linspace(0, 10, 100)
days_in_may = np.arange(1, 32)  # Days of May
y_percentages = np.random.uniform(
    0, 100, size=(31, 4))  # 31 days and 4 histograms

y_percentages = np.random.uniform(
    0, 100, size=(31, 4))  # 31 days and 4 histograms

# Calculate the week numbers for each day
week_numbers = np.array([date // 7 + 1 for date in days_in_may])

# Create a new PDF
pdf = matplotlib.backends.backend_pdf.PdfPages('graphs.pdf')

# Create a new figure for Page 1
fig_hist = plt.figure(figsize=(10, 8))

# Create a 2x2 grid of subplots
grid_spec_hist = fig_hist.add_gridspec(2, 2)

# Add histograms to each subplot
hist_ax1 = fig_hist.add_subplot(grid_spec_hist[0, 0])
week_numbers_unique = np.unique(week_numbers)
week_avg_percentages = [np.mean(
    y_percentages[week_numbers == week], axis=0) for week in week_numbers_unique]

for k, v in weekly_graph_plot_data_av.items():
    hist_ax1.bar(
        k, v, color='#d41524',
        edgecolor='white', label=f"Week {k}"
    )

hist_ax1.set_title('Évolution de Disponibilité par semaine:')
hist_ax1.set_xticks([weekly_data[i][0] for i in range(len(weekly_data))])
hist_ax1.set_xticklabels(
    [f"Week {week}" for week in [weekly_data[i][0] for i in range(len(weekly_data))]], rotation='vertical')
hist_ax1.set_xlabel('Semaines de Mai')
hist_ax1.set_ylabel('Pourcentage')

hist_ax2 = fig_hist.add_subplot(grid_spec_hist[0, 1])

for k, v in weekly_graph_plot_data_av.items():
    hist_ax1.bar(
        k, v, color='#d41524',
        edgecolor='white', label=f"Week {k}"
    )

for i, avg_percentage in enumerate(week_avg_percentages):
    hist_ax2.bar(
        i + 1, avg_percentage[1], color='orange',
        edgecolor='white', label=f"Week {i+1}"
    )
hist_ax2.set_title('Évolution de Performance par semaine:')
hist_ax2.set_xticks(np.arange(1, len(week_numbers_unique) + 1))
hist_ax2.set_xticklabels(
    [f"Week {week}" for week in week_numbers_unique], rotation='vertical')
hist_ax2.set_xlabel('Semaines de Mai')
hist_ax2.set_ylabel('Pourcentage')

# Replace hist_ax3 with a line chart
line_ax3 = fig_hist.add_subplot(grid_spec_hist[1, 0])
for i, avg_percentage in enumerate(week_avg_percentages):
    line_ax3.plot(
        days_in_may, y_percentages[:, 2], color='#dab6b9',
        marker='o', markerfacecolor='red', label=f"Week {i+1}"
    )
line_ax3.set_title('Évolution de Disponibilité par jour')
line_ax3.set_xticks(days_in_may)
# Rotate labels vertically
line_ax3.set_xticklabels(
    [str(day) for day in days_in_may], rotation='vertical'
)
line_ax3.set_xlabel('Jours de Mai')
line_ax3.set_ylabel('Pourcentage')

# Replace hist_ax4 with a line chart
line_ax4 = fig_hist.add_subplot(grid_spec_hist[1, 1])
for i, avg_percentage in enumerate(week_avg_percentages):
    line_ax4.plot(
        days_in_may, y_percentages[:, 3], color='#dab6b9',
        marker='o', markerfacecolor='red', label=f"Week {i+1}"
    )
line_ax4.set_title('Évolution de Performance par jour')
line_ax4.set_xticks(days_in_may)
# Rotate labels vertically
line_ax4.set_xticklabels(
    [str(day) for day in days_in_may], rotation='vertical'
)
line_ax4.set_xlabel('Jours de Mai')
line_ax4.set_ylabel('Pourcentage')

# Adjust layout of the second page
plt.tight_layout()

# Save the figure on Page 1
pdf.savefig(fig_hist)
plt.close(fig_hist)

# Close the PDF
pdf.close()

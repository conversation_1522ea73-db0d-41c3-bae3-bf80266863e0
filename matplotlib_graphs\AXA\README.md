# building image:

export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080

cd /data2/dockerized_rpa_v2/matplotlib_graphs/AX<PERSON>

podman build -t axa-automated-cmplx .

# running scenarios or simply run the script run_docker_scenarios.sh

sh run_docker_scenarios.sh

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR" "2023-08-01" "2023-08-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP" "2023-08-01" "2023-08-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 3 : GTA" "2023-08-01" "2023-08-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail" "2023-08-01" "2023-08-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 5 : Portail de services" "2023-08-01" "2023-08-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités" "2023-08-01" "2023-08-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel" "2023-08-01" "2023-08-31" "AXAE5"
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx "PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique" "2023-08-01" "2023-08-31" "AXAE5"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/AXA/pdfs:/app/output axa-automated-cmplx


docker run -it axa-automated-cmplx /bin/bash
podman exec -it  axa-automated-cmplx /bin/bash

podman run --rm axa-automated-cmplx



podman save -o axa-automated-cmplx.tar axa-automated-cmplx
scp axa-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/axa-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913

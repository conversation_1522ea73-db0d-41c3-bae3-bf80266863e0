#!/usr/bin/env python3
r"""
Automated File Management Script for AXA Scenario Scripts

This script performs daily maintenance on screenshots and logs folders:
- Archives screenshots from previous days into ZIP files
- Archives log files from previous days into ZIP files
- Cleans up old ZIP archives (keeps current day + 1 previous day)
- Preserves uncompressed PNG files and log files from today

Usage:
    python cleanup_files.py              # Normal execution
    python cleanup_files.py --dry-run    # Show what would be done without executing

For Windows Task Scheduler:
    Schedule to run daily at a convenient time (e.g., 23:00)
    Command: python "C:\path\to\Complexe\cleanup_files.py"
    Start in: C:\path\to\Complexe\

Author: Automated File Management System
Date: 2025-01-06
"""

import os
import sys
import zipfile
import glob
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path


def setup_logging():
    """Set up logging configuration."""
    log_filename = f"cleanup_files_{datetime.now().strftime('%Y%m%d')}.log"
    log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), log_filename)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler(log_path), logging.StreamHandler(sys.stdout)],
    )
    return logging.getLogger(__name__)


def find_target_folders(base_dir):
    """
    Recursively find all 'screenshots' and 'logs' folders within subdirectories.

    Args:
        base_dir (str): Base directory to search from (Complexe directory)

    Returns:
        dict: Dictionary with 'screenshots' and 'logs' keys containing lists of folder paths
    """
    target_folders = {"screenshots": [], "logs": []}

    try:
        for root, dirs, _ in os.walk(base_dir):
            if "screenshots" in dirs:
                screenshots_path = os.path.join(root, "screenshots")
                target_folders["screenshots"].append(screenshots_path)
                logging.info(f"Found screenshots folder: {screenshots_path}")

            if "logs" in dirs:
                logs_path = os.path.join(root, "logs")
                target_folders["logs"].append(logs_path)
                logging.info(f"Found logs folder: {logs_path}")

    except Exception as e:
        logging.error(f"Error scanning for target folders: {e}")

    return target_folders


def get_files_by_date(target_dir, file_pattern):
    """
    Get files grouped by their creation date.

    Args:
        target_dir (str): Path to target directory
        file_pattern (str): File pattern to match (e.g., "*.png", "*.log")

    Returns:
        dict: Dictionary with date strings as keys and lists of file paths as values
    """
    files_by_date = {}

    try:
        pattern = os.path.join(target_dir, file_pattern)
        files = glob.glob(pattern)

        for file_path in files:
            try:
                # Get file creation time
                creation_time = os.path.getctime(file_path)
                creation_date = datetime.fromtimestamp(creation_time).strftime("%Y%m%d")

                if creation_date not in files_by_date:
                    files_by_date[creation_date] = []

                files_by_date[creation_date].append(file_path)

            except Exception as e:
                logging.warning(f"Error processing file {file_path}: {e}")

    except Exception as e:
        logging.error(
            f"Error scanning files in {target_dir} with pattern {file_pattern}: {e}"
        )

    return files_by_date


def create_daily_archive(target_dir, date_str, files_list, archive_type, dry_run=False):
    """
    Create a ZIP archive for files from a specific date.

    Args:
        target_dir (str): Path to target directory
        date_str (str): Date string in YYYYMMDD format
        files_list (list): List of file paths to archive
        archive_type (str): Type of archive ("screenshots" or "logs")
        dry_run (bool): If True, only show what would be done

    Returns:
        str: Path to created ZIP file, or None if failed
    """
    if not files_list:
        return None

    zip_filename = f"{archive_type}_{date_str}.zip"
    zip_path = os.path.join(target_dir, zip_filename)

    if dry_run:
        logging.info(f"[DRY RUN] Would create archive: {zip_path}")
        logging.info(f"[DRY RUN] Would archive {len(files_list)} {archive_type} files from {date_str}")
        return zip_path

    try:
        with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for file_path in files_list:
                # Use only the filename in the archive (not full path)
                arcname = os.path.basename(file_path)
                zipf.write(file_path, arcname)
                logging.debug(f"Added to archive: {arcname}")

        logging.info(f"Created archive: {zip_path} with {len(files_list)} files")
        return zip_path

    except Exception as e:
        logging.error(f"Error creating archive {zip_path}: {e}")
        return None


def delete_archived_files(files_list, dry_run=False):
    """
    Delete files that have been successfully archived.

    Args:
        files_list (list): List of file paths to delete
        dry_run (bool): If True, only show what would be done
    """
    for file_path in files_list:
        if dry_run:
            logging.info(f"[DRY RUN] Would delete archived file: {file_path}")
        else:
            try:
                os.remove(file_path)
                logging.debug(f"Deleted archived file: {file_path}")
            except Exception as e:
                logging.warning(f"Error deleting file {file_path}: {e}")


def cleanup_old_archives(target_dir, archive_type, keep_days=2, dry_run=False):
    """
    Delete ZIP archives older than specified number of days.

    Args:
        target_dir (str): Path to target directory
        archive_type (str): Type of archive ("screenshots" or "logs")
        keep_days (int): Number of days to keep (including today)
        dry_run (bool): If True, only show what would be done
    """
    try:
        zip_pattern = os.path.join(target_dir, f"{archive_type}_*.zip")
        zip_files = glob.glob(zip_pattern)

        cutoff_date = datetime.now() - timedelta(days=keep_days)

        for zip_file in zip_files:
            try:
                # Extract date from filename (archive_type_YYYYMMDD.zip)
                filename = os.path.basename(zip_file)
                prefix = f"{archive_type}_"
                if filename.startswith(prefix) and filename.endswith(".zip"):
                    date_start = len(prefix)
                    date_end = date_start + 8
                    date_part = filename[date_start:date_end]  # Extract YYYYMMDD
                    file_date = datetime.strptime(date_part, "%Y%m%d")

                    if file_date < cutoff_date:
                        if dry_run:
                            logging.info(
                                f"[DRY RUN] Would delete old archive: {zip_file}"
                            )
                        else:
                            os.remove(zip_file)
                            logging.info(f"Deleted old archive: {zip_file}")

            except Exception as e:
                logging.warning(f"Error processing archive {zip_file}: {e}")

    except Exception as e:
        logging.error(f"Error cleaning up old archives in {target_dir}: {e}")


def process_target_folder(target_dir, folder_type, file_pattern, dry_run=False):
    """
    Process a single target folder: archive, cleanup, preserve recent files.

    Args:
        target_dir (str): Path to target directory
        folder_type (str): Type of folder ("screenshots" or "logs")
        file_pattern (str): File pattern to match (e.g., "*.png", "*.log")
        dry_run (bool): If True, only show what would be done
    """
    logging.info(f"Processing {folder_type} folder: {target_dir}")

    # Get files grouped by date
    files_by_date = get_files_by_date(target_dir, file_pattern)

    if not files_by_date:
        logging.info(f"No {file_pattern} files found in {target_dir}")
        return

    today = datetime.now().strftime("%Y%m%d")

    # Process each date
    for date_str, files_list in files_by_date.items():
        if date_str == today:
            # Preserve today's files - don't archive or delete them
            logging.info(
                f"Preserving {len(files_list)} {file_pattern} files from today ({date_str})"
            )
            continue

        # Archive files from previous days
        zip_path = create_daily_archive(
            target_dir, date_str, files_list, folder_type, dry_run
        )

        if zip_path and not dry_run:
            # Only delete original files if archive was created successfully
            if os.path.exists(zip_path):
                delete_archived_files(files_list, dry_run)
                logging.info(
                    f"Archived and cleaned up {len(files_list)} {file_pattern} files from {date_str}"
                )
        elif zip_path and dry_run:
            delete_archived_files(files_list, dry_run)

    # Cleanup old archives (keep current day + 1 previous day)
    cleanup_old_archives(target_dir, folder_type, keep_days=2, dry_run=dry_run)


def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(
        description="Automated File Management for AXA Scenario Scripts",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python cleanup_files.py              # Normal execution
    python cleanup_files.py --dry-run    # Show what would be done

For Windows Task Scheduler:
    Schedule to run daily at 23:00
    Command: python "C:\\\\path\\\\to\\\\Complexe\\\\cleanup_files.py"
    Start in: C:\\\\path\\\\to\\\\Complexe\\\\
        """,
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually executing operations",
    )

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging()

    # Get the script directory (should be Complexe directory)
    script_dir = os.path.dirname(os.path.abspath(__file__))

    if args.dry_run:
        logger.info("=== DRY RUN MODE - No files will be modified ===")

    logger.info(f"Starting file cleanup process in: {script_dir}")
    logger.info(f"Current date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Find all target folders (screenshots and logs)
    target_folders = find_target_folders(script_dir)

    total_folders = len(target_folders["screenshots"]) + len(target_folders["logs"])
    if total_folders == 0:
        logger.info("No screenshots or logs folders found")
        return

    logger.info(f"Found {len(target_folders['screenshots'])} screenshots folders")
    logger.info(f"Found {len(target_folders['logs'])} logs folders")
    logger.info(f"Total folders to process: {total_folders}")

    # Process screenshots folders
    for screenshots_dir in target_folders["screenshots"]:
        try:
            process_target_folder(
                screenshots_dir, "screenshots", "*.png", dry_run=args.dry_run
            )
        except Exception as e:
            logger.error(f"Error processing screenshots folder {screenshots_dir}: {e}")

    # Process logs folders
    for logs_dir in target_folders["logs"]:
        try:
            process_target_folder(logs_dir, "logs", "*.log", dry_run=args.dry_run)
        except Exception as e:
            logger.error(f"Error processing logs folder {logs_dir}: {e}")

    logger.info("File cleanup process completed")


if __name__ == "__main__":
    main()

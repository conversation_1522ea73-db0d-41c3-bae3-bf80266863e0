# dockerized_rpa_v2/Complexe/Dockerfile

FROM oraclelinux:8

# 1. Install system deps
RUN dnf install -y \
        gcc-c++ \
        python39-devel \
        firefox \
        wget \
        tar \
    && dnf clean all

# 2. Install geckodriver
COPY geckodriver-v0.33.0-linux32.tar.gz /usr/local/bin/
RUN tar -xf /usr/local/bin/geckodriver-v0.33.0-linux32.tar.gz -C /usr/local/bin/ \
 && chmod +x /usr/local/bin/geckodriver \
 && rm /usr/local/bin/geckodriver-v0.33.0-linux32.tar.gz

# 3. Set up application code
WORKDIR /app
# Copy everything (including your per-client folders)
COPY . .

# 4. Install Python requirements
RUN python3 -m pip install --upgrade pip \
 && pip3 install --no-cache-dir -r requirements.txt

# 5. Copy + normalize + make entrypoint executable
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN sed -i 's/\r$//' /usr/local/bin/entrypoint.sh \
 && chmod +x /usr/local/bin/entrypoint.sh

# 6. Default command
CMD ["/usr/local/bin/entrypoint.sh"]

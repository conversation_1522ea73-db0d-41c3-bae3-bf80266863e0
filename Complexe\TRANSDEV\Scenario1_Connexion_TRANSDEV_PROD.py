#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
    output_failure_results,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["transdev"]["username"], credentials["transdev"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-transdev.soprahronline.sopra/pleiades2/portal/index.jsp"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Load credentials
try:
    username, password = load_credentials()
except Exception as e:
    logging.error(f"Failed to load credentials: {e}")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    #! transaction_1: Navigate to TRANSDEV Pleiades
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to TRANSDEV Pleiades",
        measure_time=True,
        unique_str="step1",
        total_steps=1,
    )

    # Step 2: Take screenshot before login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 2: Screenshot before login"),
        "Step 2: Take screenshot before login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_2: Enter credentials and login
    # Step 3: Locate and enter username
    username_field = check_and_log(
        test.driver,
        test.transdev_locate_username_field,
        "Step 3: Locate username field",
        measure_time=True,
        unique_str="step345",
        total_steps=3,
    )

    check_and_log(
        test.driver,
        lambda: username_field.send_keys(username),
        "Step 4: Enter username",
        measure_time=True,
        unique_str="step345",
    )

    # Step 5: Locate and enter password
    password_field = check_and_log(
        test.driver,
        test.transdev_locate_password_field,
        "Step 5: Locate password field",
        measure_time=True,
        unique_str="step345",
    )

    check_and_log(
        test.driver,
        lambda: password_field.send_keys(password),
        "Step 5: Enter password",
        measure_time=True,
        unique_str="step345",
    )

    #! transaction_3: Login and verify success
    check_and_log(
        test.driver,
        test.transdev_click_login_button,
        "Step 6: Click login button",
        measure_time=True,
        unique_str="step6",
        total_steps=1,
    )

    # Step 7: Take screenshot after successful login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 7: Screenshot after successful login"),
        "Step 7: Take screenshot after successful login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_4: Logout
    check_and_log(
        test.driver,
        test.transdev_click_exit_button,
        "Step 8: Click exit button",
        measure_time=True,
        unique_str="step8",
        total_steps=1,
    )

    # Step 9: Take final screenshot
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 9: Final screenshot after logout"),
        "Step 9: Take final screenshot after logout",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        is_last_step=True,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")
    # Output failure results to stdout
    output_failure_results()

finally:
    # Cleanup
    test.teardown_driver()

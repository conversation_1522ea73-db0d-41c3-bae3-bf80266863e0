<!DOCTYPE HTML>
<html class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="x-ua-compatible" content="ie=edge">
	<title data-i18n="appforyou_title"></title>
	<meta name="description" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=1">
	<link rel="shortcut icon" type="image/png" href="/theme/images/favicon.png" />
</head>

<body class="">
	<div class="pt-wrapper foryou-header-nav">

		<!-- Main Menu -->
		<div class="main-menu" style="display: none;">

			<div class="left-part">
				<button id="burgerMenuButton" class="access-menu" data-i18n="[title]appforyou_title_menu">
					<i class="fa fa-bars fa-lg"></i>
				</button>
				<div class="logosoprahr pt-trigger" data-animation="2" data-goto="1" data-i18n="[title]appforyou_title_home">
					<a id="logosoprahr" href="javascript:void(0);">
						<svg aria-hidden="true" style="fill: #ffffff; height: 30px; padding-left: 12px; padding-right: 12px; width: 54px; margin-top: 6px;"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#Logo-4YOU"></use></svg>
					</a>
				</div>
				<div id="btns-spaces">
					<div class="space-switcher">
						<a href="#/syd" id="space-switcher-link">
							<span id="manager" data-i18n="[title]appforyou_title_manager">
								<svg aria-hidden="true" class="shrs-icon">
									<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-person"></use>
								</svg>
							</span>
							<span id="expert" data-i18n="[title]appforyou_title_expert">
								<svg aria-hidden="true" class="shrs-icon">
									<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-gestionnaire"></use>
								</svg>
							</span>
						</a>
						<div class="black-disk shrs-transition"></div>
					</div>
					<h3 class="space-name"></h3>
				</div>
			</div>
			<h1 id="pageTitle"></h1>
			<div class="right-part">
				<div id="logo-space">
					<img id="logo-entreprise" src="/theme/images/logoEntreprise_blank.png">
				</div>
				<div id="search-space" class="search-space-content hide">
					<div id="navbarInputSearch" class="bare">
						<label class="hide" for="navBarSearchTextId"></label>
						<input id="navBarSearchTextId" class="ui-autocomplete-input" type="text" name="navBar-search-text"
						data-i18n="[placeholder]appforyou_placeholder_search" autocomplete="off" min=2 />
						<a class="search_icon" data-i18n="[title]appforyou_title_search">
							<svg aria-hidden="true" class="shrs-button__icon shrs-icon shrs-icon_size_large">
								<use xmlns:xlink="http://www.w3.org/1999/xlink"
									xlink:href="#shrs-icon-search"></use>
							</svg>
						</a>
						<a href="javascript:void(0)" class="search-close" data-i18n="[title]appforyou_title_search_close">
							<i class="fa fa-times"></i>
						</a>
					</div>
				</div>
				<div id="question-space">
					<a href="#/demarches/ticketing/create">
						<div id="inputquestion" data-i18n="[title]appforyou_title_help">
							<svg aria-hidden="true" class="shrs-icon shrs-question-mark shrs-icon_size_large">
								<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-help"></use>
							</svg>
						</div>
					</a>
				</div>
				<div class="navbar-logout-space pull-right" data-i18n="[title]appforyou_title_logout">
					<a href="#/logout" id="navbar-logout-link-id" class="navbar-logout-link">
						<svg aria-hidden="true" class="shrs-icon navbar-logout-icon shrs-icon_size_large">
							<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-shutdown"></use>
						</svg>
					</a>
				</div>
			</div>

			<script id="main-menu-script" type="text/html">
				{{#each this.menu_content}}
					{{>menu-items}}
				{{/each}}
			</script>
			<script id="menu-items" type="test/html">
				{{#if !this.uiConfig.hidden}}
					{{#if this.id === 'legacyMenuId'}}
						{{#if this.children}}
							{{>menuLegacy-items}}
						{{else}}
							{{>menuLegacy-noitems}}
						{{/if}}
					{{else}}
						{{>menuForyou-items}}
					{{/if}}
				{{/if}}
			</script>

			<script id="menuLegacy-items" type="test/html">
				{{#if (this.id !== 'legacyMenuId') && (this.children === undefined || !this.children || this.children.length === 0)}}
					<li class="white clickable {{#if this.legacyRoles !== undefined && this.legacyRoles}}has-multi-roles{{else}}empty-menu-items{{/if}}">
						{{>menuLegacy-item}}
					</li>
				{{else}}
					<li class="white submenu {{>menuLegacy-attributes}}">
						{{>menuLegacy-root-item}}
						{{#if this.children.length > 0}}
							<ul>
								{{#each this.children}}
									{{>menuLegacy-items}}
								{{/each}}
							</ul>
						{{/if}}
					</li>
				{{/if}}
			</script>
			<script id="menuLegacy-attributes" type="test/html">
				{{#if this.children.length === 0}}
					empty-menu-items
				{{/if}}
				{{#if this.uiConfig.classes !== undefined}}
					{{#each this.uiConfig.classes}}
						{{this}}
					{{/each}}
				{{/if}}
			</script>

			<script id="menuLegacy-item" type="text/html">
				<a class="{{this.accessMode === 'standalone' ? 'legacy-menu-item-standalone-mode' : 'link-to-open-in-same-tab'}}" href = "javascript:void(0);" on-click="handleLegacyItemClick(this.accessMode, this.category, this.id, this.name, this.fullName, this.link, this.legacyRoles)">
					<span>{{this.name}}</span>
				</a>
				{{#if this.accessMode !== 'standalone' && this.accessMode !== 'embedded'}}
					{{#if this.legacyRoles === undefined || !this.legacyRoles}}
						<a class="link-to-open-in-new-tab" href="{{this.link}}" target="_blank">
							<img class="open-in-new-tab-icon" src="/theme/images/wte_new_window.gif" alt="open in new tab" />
						</a>
					{{else}}
						<a class="link-to-open-in-new-tab" href="javascript:void(0);" on-click="openLegacyRolesPopup(this.category, this.id, this.name, this.fullName, this.link, this.legacyRoles)">
							<img class="open-in-new-tab-icon" src="/theme/images/wte_new_window.gif" alt="open in new tab" />
						</a>
					{{/if}}
				{{/if}}
			</script>

			<script id="menuLegacy-root-item" type="text/html">
				<a href="javascript:void(0);">
					{{#if this.uiConfig.icon !== undefined}}
						<svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{this.uiConfig.icon}}"></use></svg>
					{{/if}}
					<span>{{this.name}}</span>
				</a>
				{{#if this.id !== undefined && this.id === 'legacyMenuId'}}
					<span id="loading-legacy-site-map-icon"></span>
				{{/if}}
				{{#if this.children.length > 0}}
					<i class="fa fa-caret-right fa-lg sub-menu-icon" aria-hidden="true"></i>
				{{/if}}
			</script>

			<script id="menuLegacy-noitems" type="test/html">
				<li class="{{this.uiConfig.theme ? this.uiConfig.theme : ''}}" on-click="openLegacyRolesPopup(null, null, null, null, this.link, null)">
					{{>menu-item}}
				</li>
			</script>
			
			<script id="menuForyou-items" type="test/html">
				{{#if this.subItems === undefined}}
					<li class="{{this.uiConfig.theme ? this.uiConfig.theme : ''}} {{this.subItems ? 'submenu' : 'clickable'}}" on-click="handleClick(this.action)">
						{{>menu-item}}
					</li>
				{{/if}}

				{{#if this.subItems !== undefined}}
					<li class="{{this.uiConfig.theme ? this.uiConfig.theme : ''}} {{this.subItems ? 'submenu' : 'clickable'}}">
						{{>submenu-root-item}}
						<ul>
							{{#each this.subItems}}
								{{>menu-items}}
							{{/each}}
						</ul>
					</li>
				{{/if}}
			</script>

			<script id="menu-item" type="text/html">
				<a {{>menu-item-attr}}>
					{{#if this.uiConfig.icon !== undefined}}
                    	<svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{this.uiConfig.icon}}"></use></svg>
					{{/if}}
					<span>{{this.label}}</span>
				</a>
				{{#if this.action.openinothertab !== undefined && this.action.openinothertab}}
					<i class="fa fa-files-o fa-lg open-in-new-tab-icon" aria-hidden="true"></i>
				{{/if}}
			</script>
			<script id="submenu-root-item" type="text/html">
				<a {{>menu-item-attr}}>

					{{#if this.uiConfig.icon !== undefined}}
                	    <svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{this.uiConfig.icon}}"></use></svg>
					{{/if}}
					<span>{{this.label}}</span>
				</a>
				{{#if this.subItems.length > 0}}
					<i class="fa fa-caret-right fa-lg sub-menu-icon hidden-xs" aria-hidden="true"></i>
				{{/if}}
			</script>

			<script id="menu-item-attr" type="text/html">
				id={{this.id}}
				{{#if this.uiConfig.classes !== undefined}}
					class="{{#each this.uiConfig.classes}}
							{{this}}
						{{/each}}"
				{{/if}}
				{{#each this.uiConfig.attributes}}
					{{this.attr}} = "{{this.value}}"
				{{/each}}
				href = {{(this.action !== undefined && this.action.href !== undefined) ? this.action.href : "javascript:void(0);"}}
			</script>
		</div>
		<ul id="mainMenuList"></ul>
		<div class="mobile-header" style="display: none;">
			<div class="col-xs-10">
				<div id="mobile-pageTitle"></div>
			</div>
			<div class="col-xs-2 text-right">
				<button class="showInfo shrs-button shrs-button_neutral shrs-button_type_icon shrs-transition" aria-label="The action behind the icon button"
				 style="display: none;">
                    <svg aria-hidden="true" class="shrs-button__icon shrs-icon shrs-icon_size_large shrs-transition">
                        <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-info"></use>
                    </svg>
                    <span class="shrs-assistive-text" data-i18n="appforyou_info"></span>
                </button>
			</div>
		</div>
		<!-- end Main Menu -->

		<!-- Pages Container -->
		<div class="spaces"></div>

	</div>

	<div id="modalPopupProgressBarDiv" class="hide">
		<button type='button' style="display:none" class='btn btn-default btn-radius-md isenabled btn_abondan' data-i18n="appforyou_button_cancel"></button>
	</div>
	<div id="4YOUmodal" class="modal fade" tabindex="-1" role="dialog" style="display: none;">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="content">
					<div id="modalPopupHeader" class="header"></div>
					<div id="modalPopupBodySpinner" class="hide body"></div>
					<div id="modalPopupBody" class="body"></div>
					<div id="modalPopupFooter" class="footer"></div>
				</div>
				<div class="closeButton">
					<i class="fa fa-times" data-dismiss="modal" aria-label="Close"></i>
				</div>
			</div>
			<!-- /.modal-content -->
		</div>
		<!-- /.modal-dialog -->
	</div>
	<!-- /.modal -->

	<!-- TEMPLATES -->
	<script type="text/x-handlebars" id="tpl-grid">
		{{#each grids}}
		<div id="content" class="pt-page pt-page-{{ pageNumber }}" data-page-name="{{ pageName }}" data-page-number="{{ pageNumber }}">
			<div id="{{ gridId }}" class="grid-stack large"></div>
		</div>
		{{/each}}
	</script>
	<style type="text/css">
		#mainMenuList a{
			text-decoration: none;
		}
	</style>

	<!-- End Pages Container -->
	<script data-main="main.js" src="/resources/js/lib/require/require-2.1.17.min.js"></script>
</body>

</html>

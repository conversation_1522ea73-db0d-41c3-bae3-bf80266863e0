#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["axa"]["username"], credentials["axa"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-adp-axa.soprahronline.sopra/app/foryou/#/login"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    exit(1)

# Load credentials
username, password = load_credentials()

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    #! transaction_1
    #! Demander la page de l'application
    #! Une page d'accueil s'affiche
    # Step 1, 2, 3, 4 grouped together under "step1234"
    # Step 1: Navigate to the URL
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to the URL",
        measure_time=True,
        unique_str="step1234",
        total_steps=4,  # Define total steps in the group
    )
    # Step 2: Wait for the page to load
    check_and_log(
        test.driver,
        test.wait_for_page_load,
        "Step 2: Wait for the page to load",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 3: Locate the shadow host element
    shadow_host = check_and_log(
        test.driver,
        test.locate_shadow_host,
        "Step 3: Locate shadow host",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 4: Access the shadow root
    test.shadow_root = check_and_log(
        test.driver,
        lambda: test.access_shadow_root(shadow_host),
        "Step 4: Access shadow root",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 5: Take screenshot before login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 5: Screenshot before login"),
        "Step 5: Take screenshot before login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_2
    #! Une fenêtre d'authentification s'affiche
    #! Saisir les champs suivants :
    #! Identifiant 	LOGIN Utilisateur S635525
    #! Mot de passe 	Password Utilisateur 0S635525
    #! Cliquer sur le bouton « Me connecter »
    # Step 6, 7, 8, 9, 10, 11 grouped together under "step6789101"
    # Step 6: Locate username field
    username_field = check_and_log(
        test.driver,
        test.locate_username_field,
        "Step 6: Locate username field",
        unique_str="step6789101",
        measure_time=True,
        total_steps=6,
    )

    # Step 7: Enter username
    check_and_log(
        test.driver,
        lambda: username_field.send_keys(username),
        "Step 7: Enter username",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 8: Locate password field
    password_field = check_and_log(
        test.driver,
        test.locate_password_field,
        "Step 8: Locate password field",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 9: Enter password
    check_and_log(
        test.driver,
        lambda: password_field.send_keys(password),
        "Step 9: Enter password",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 10: Locate login button
    login_button = check_and_log(
        test.driver,
        test.locate_login_button,
        "Step 10: Locate login button",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 11: Click login button
    check_and_log(
        test.driver,
        lambda: login_button.click(),
        "Step 11: Click login button",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 12: Take screenshot after successful login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 12: Screenshot after successful login"),
        "Step 12: Take screenshot after successful login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_3
    #! Le menu d'accueil d'Espace gestionnaire s'affiche.
    # Step 11, 12 grouped together under "step1112"
    # Step 11: Verify if '#btns-spaces > h3' exists
    check_and_log(
        test.driver,
        lambda: test.locate_espace_gestionnaire_menu,
        "Step 11: Verify if '#btns-spaces > h3' exists",
        measure_time=True,
        unique_str="step1112",
        total_steps=2,
    )

    # Step 12: Take screenshot
    check_and_log(
        test.driver,
        test.take_screenshot,
        "Step 12: Take screenshot",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_4
    #! Dans la liste déroulante du menu, sélectionner le menu
    #! « Gestion administrative/Paie », puis sélectionner le menu
    #! « Ressources humaines » ensuite sélectionner le menu « Collaborateur »
    #! Cliquer sur le menu « Détail contrat de travail »
    # Step 13, 14, 15, 16, 17 grouped together under "step1314151617"
    # Step 13: Click burger menu button
    check_and_log(
        test.driver,
        test.click_burger_menu_button,
        "Step 13: Click burger menu button",
        measure_time=True,
        unique_str="step1314151617",
        total_steps=5,
    )

    # Step 14: Click Gestion administrative/Paie link
    check_and_log(
        test.driver,
        test.click_gestion_administrative_paie_link,
        "Step 14: Click Gestion administrative/Paie link",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 15: Click nth child 1 span (Ressources humaines)
    check_and_log(
        test.driver,
        test.click_nth_child_1_span,
        "Step 15: Click nth child 1 span (Ressources humaines)",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 16: Click Contrat link
    check_and_log(
        test.driver,
        test.click_contrat_link,
        "Step 16: Click Contrat link",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 17: Click Detail contrat de travail
    check_and_log(
        test.driver,
        test.click_detail_contrat_travail,
        "Step 17: Click Detail contrat de travail",
        measure_time=True,
        unique_str="step1314151617",
    )

    #! transaction_5
    #! La page de recherche du « Détail contrat de travail » s'affiche
    #! Dans la page de recherche, sélectionner les critères de recherche ci-dessous :
    #! Nom commence par : saisir « DUVAL »
    #! Cliquer sur le bouton « Rechercher »
    # Step 18, 19, 20, 21 grouped together under "step18192021"
    # Step 18: Switch to frame
    check_and_log(
        test.driver,
        test.switch_to_frame,
        "Step 18: Switch to frame",
        measure_time=True,
        unique_str="step18192021",
        total_steps=4,
    )

    # Step 19: Locate and prepare name field
    name_field = check_and_log(
        test.driver,
        test.locate_name_field,
        "Step 19: Locate and prepare name field",
        measure_time=True,
        unique_str="step18192021",
    )

    # Step 20: Enter name DUVAL with enhanced error handling
    def enter_name_safely():
        try:
            # Clear the field first
            name_field.clear()
            # Enter the text
            name_field.send_keys("DUVAL")
            logging.info("Successfully entered 'DUVAL' in name field")
        except Exception as e:
            logging.error(f"Failed to enter name: {e}")
            # Try alternative approach - re-locate and try again
            import time

            time.sleep(1)
            new_field = test.locate_name_field()
            new_field.clear()
            new_field.send_keys("DUVAL")
            logging.info("Successfully entered 'DUVAL' using alternative approach")

    check_and_log(
        test.driver,
        enter_name_safely,
        "Step 20: Enter name DUVAL",
        measure_time=True,
        unique_str="step18192021",
    )

    # Step 21: Click search button
    check_and_log(
        test.driver,
        test.click_search_button,
        "Step 21: Click search button",
        measure_time=True,
        unique_str="step18192021",
    )

    #! transaction_6
    #! Les résultats de recherche s'affichent
    #! Cliquer sur la première ligne dans les résultats de la recherche
    #! La page de détail du contrat de travail du salarié s'affiche
    # Step 22 grouped under "step22"
    # Step 22: Click first result
    check_and_log(
        test.driver,
        test.click_first_result,
        "Step 22: Click first result",
        measure_time=True,
        unique_str="step22",
        total_steps=1,
    )

    #! transaction_7
    #! Déconnexion
    #! Cliquer sur le bouton « Se déconnecter ».
    # Step 23, 24 grouped together under "step2324"
    # Step 23: Switch to default content
    check_and_log(
        test.driver,
        test.switch_to_default_content,
        "Step 23: Switch to default content",
        measure_time=True,
        unique_str="step2324",
        total_steps=2,
    )

    # Step 24: Click logout button
    logout_button = check_and_log(
        test.driver,
        test.locate_logout_button,
        "Step 24: Locate logout button",
        measure_time=True,
        unique_str="step2324",
    )

    check_and_log(
        test.driver,
        lambda: logout_button.click(),
        "Step 24: Click logout button",
        measure_time=True,
        unique_str="step2324",
    )

    #! transaction_8
    #! L'utilisateur est déconnecté de l'application
    #! La page d'accueil de l'application s'affiche
    # Step 25: Take final screenshot
    check_and_log(
        test.driver,
        test.take_screenshot,
        "Step 25: Take final screenshot",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        is_last_step=True,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")

finally:
    # Cleanup
    test.teardown_driver()

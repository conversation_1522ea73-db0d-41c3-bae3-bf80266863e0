#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["axa"]["username"], credentials["axa"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-adp-axa.soprahronline.sopra/app/foryou/#/login"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    exit(1)

# Load credentials
username, password = load_credentials()

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    #! transaction_1
    #! Demander la page de l'application
    #! Une page d'accueil s'affiche
    # Step 1, 2, 3, 4 grouped together under "step1234"
    # Step 1: Navigate to the URL
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to the URL",
        measure_time=True,
        unique_str="step1234",
        total_steps=4,  # Define total steps in the group
    )
    # Step 2: Wait for the page to load
    check_and_log(
        test.driver,
        test.wait_for_page_load,
        "Step 2: Wait for the page to load",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 3: Locate the shadow host element
    shadow_host = check_and_log(
        test.driver,
        test.locate_shadow_host,
        "Step 3: Locate shadow host",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 4: Access the shadow root
    test.shadow_root = check_and_log(
        test.driver,
        lambda: test.access_shadow_root(shadow_host),
        "Step 4: Access shadow root",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 5: Take screenshot before login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 5: Screenshot before login"),
        "Step 5: Take screenshot before login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    # Transaction 1 completion: Application page request and initial setup
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Transaction_1_Complete_Application_Setup"),
        "Transaction 1: Application page request and initial setup - Complete",
        measure_time=True,
        unique_str="transaction1",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_2
    #! Une fenêtre d'authentification s'affiche
    #! Saisir les champs suivants :
    #! Identifiant 	LOGIN Utilisateur S635525
    #! Mot de passe 	Password Utilisateur 0S635525
    #! Cliquer sur le bouton « Me connecter »
    # Step 6, 7, 8, 9, 10, 11 grouped together under "step6789101"
    # Step 6-11: Complete AXA login process with validation
    check_and_log(
        test.driver,
        lambda: test.axa_direct_login(username, password),
        "Step 6-11: AXA login process with validation",
        measure_time=True,
        unique_str="step6789101",
        total_steps=1,
    )

    # Step 12: Take screenshot after successful login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 12: Screenshot after successful login"),
        "Step 12: Take screenshot after successful login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    # Transaction 2 completion: Authentication process
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Transaction_2_Complete_Authentication"),
        "Transaction 2: Authentication window display and login process - Complete",
        measure_time=True,
        unique_str="transaction2",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_3
    #! Le menu d'accueil d'Espace gestionnaire s'affiche.
    # Step 11, 12 grouped together under "step1112"
    # Step 11: Verify if '#btns-spaces > h3' exists
    check_and_log(
        test.driver,
        lambda: test.locate_espace_gestionnaire_menu,
        "Step 11: Verify if '#btns-spaces > h3' exists",
        measure_time=True,
        unique_str="step1112",
        total_steps=2,
    )

    # Step 12: Take screenshot
    check_and_log(
        test.driver,
        test.take_screenshot,
        "Step 12: Take screenshot",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    # Transaction 3 completion: Main menu display verification
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Transaction_3_Complete_Main_Menu_Display"),
        "Transaction 3: Main menu of Espace gestionnaire display verification - Complete",
        measure_time=True,
        unique_str="transaction3",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_4
    #! Dans la liste déroulante du menu, sélectionner le menu
    #! « Gestion administrative/Paie », puis sélectionner le menu
    #! « Ressources humaines » ensuite sélectionner le menu « Collaborateur »
    #! Cliquer sur le menu « Détail contrat de travail »
    # Step 13, 14, 15, 16, 17 grouped together under "step1314151617"
    # Step 13: Click burger menu button
    check_and_log(
        test.driver,
        test.click_burger_menu_button,
        "Step 13: Click burger menu button",
        measure_time=True,
        unique_str="step1314151617",
        total_steps=5,
    )

    # Step 14: Click Gestion administrative/Paie link
    check_and_log(
        test.driver,
        test.click_gestion_administrative_paie_link,
        "Step 14: Click Gestion administrative/Paie link",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 15: Click nth child 1 span (Ressources humaines)
    check_and_log(
        test.driver,
        test.click_nth_child_1_span,
        "Step 15: Click nth child 1 span (Ressources humaines)",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 16: Click Contrat link
    check_and_log(
        test.driver,
        test.click_contrat_link,
        "Step 16: Click Contrat link",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Step 17: Click Detail contrat de travail
    check_and_log(
        test.driver,
        test.click_detail_contrat_travail,
        "Step 17: Click Detail contrat de travail",
        measure_time=True,
        unique_str="step1314151617",
    )

    # Transaction 4 completion: Navigation to contract details menu
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Transaction_4_Complete_Menu_Navigation"),
        "Transaction 4: Navigation to contract details menu - Complete",
        measure_time=True,
        unique_str="transaction4",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_5
    #! La page de recherche du « Détail contrat de travail » s'affiche
    #! Dans la page de recherche, sélectionner les critères de recherche ci-dessous :
    #! Nom commence par : saisir « DUVAL »
    #! Cliquer sur le bouton « Rechercher »
    # Step 18, 19, 20, 21 grouped together under "step18192021"
    # Step 18: Switch to frame
    check_and_log(
        test.driver,
        test.switch_to_frame,
        "Step 18: Switch to frame",
        measure_time=True,
        unique_str="step18192021",
        total_steps=4,
    )

    # Step 19: Locate and prepare name field
    name_field = check_and_log(
        test.driver,
        test.locate_name_field,
        "Step 19: Locate and prepare name field",
        measure_time=True,
        unique_str="step18192021",
    )

    # Step 20: Enter name DUVAL with enhanced error handling
    def enter_name_safely():
        try:
            # Clear the field first
            name_field.clear()
            # Enter the text
            name_field.send_keys("DUVAL")
            logging.info("Successfully entered 'DUVAL' in name field")
        except Exception as e:
            logging.error(f"Failed to enter name: {e}")
            # Try alternative approach - re-locate and try again
            import time

            time.sleep(1)
            new_field = test.locate_name_field()
            new_field.clear()
            new_field.send_keys("DUVAL")
            logging.info("Successfully entered 'DUVAL' using alternative approach")

    check_and_log(
        test.driver,
        enter_name_safely,
        "Step 20: Enter name DUVAL",
        measure_time=True,
        unique_str="step18192021",
    )

    # Step 21: Click search button
    check_and_log(
        test.driver,
        test.click_search_button,
        "Step 21: Click search button",
        measure_time=True,
        unique_str="step18192021",
    )

    # Transaction 5 completion: Search functionality for contract details
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Transaction_5_Complete_Search_Functionality"),
        "Transaction 5: Search functionality for contract details - Complete",
        measure_time=True,
        unique_str="transaction5",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_6
    #! Les résultats de recherche s'affichent
    #! Cliquer sur la première ligne dans les résultats de la recherche
    #! La page de détail du contrat de travail du salarié s'affiche
    # Step 22 grouped under "step22"
    # Step 22: Click first result
    check_and_log(
        test.driver,
        test.click_first_result,
        "Step 22: Click first result",
        measure_time=True,
        unique_str="step22",
        total_steps=1,
    )

    # Transaction 6 completion: Search results display and selection
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Transaction_6_Complete_Search_Results"),
        "Transaction 6: Search results display and selection - Complete",
        measure_time=True,
        unique_str="transaction6",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_7
    #! Déconnexion
    #! Cliquer sur le bouton « Se déconnecter ».
    # Step 23, 24 grouped together under "step2324"
    # Step 23: Switch to default content
    check_and_log(
        test.driver,
        test.switch_to_default_content,
        "Step 23: Switch to default content",
        measure_time=True,
        unique_str="step2324",
        total_steps=2,
    )

    # Step 24: Click logout button
    logout_button = check_and_log(
        test.driver,
        test.locate_logout_button,
        "Step 24: Locate logout button",
        measure_time=True,
        unique_str="step2324",
    )

    check_and_log(
        test.driver,
        lambda: logout_button.click(),
        "Step 24: Click logout button",
        measure_time=True,
        unique_str="step2324",
    )

    # Transaction 7 completion: Logout process
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Transaction_7_Complete_Logout"),
        "Transaction 7: Logout from the application - Complete",
        measure_time=True,
        unique_str="transaction7",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_8
    #! L'utilisateur est déconnecté de l'application
    #! La page d'accueil de l'application s'affiche
    # Step 25: Take final screenshot
    check_and_log(
        test.driver,
        test.take_screenshot,
        "Step 25: Take final screenshot",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        is_last_step=True,
    )

    # Transaction 8 completion: Final verification after logout
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Transaction_8_Complete_Final_Verification"),
        "Transaction 8: Final verification - user logged out and home page displayed - Complete",
        measure_time=True,
        unique_str="transaction8",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
        is_last_step=True,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")

finally:
    # Cleanup
    test.teardown_driver()

#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["axa"]["username"], credentials["axa"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-adp-axa.soprahronline.sopra/app/foryou/#/login"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    exit(1)

# Load credentials
username, password = load_credentials()

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    #! transaction_1
    #! Demander la page de l'application
    #! Une page d'accueil s'affiche
    # Transaction 1: Application page request and initial setup
    def transaction_1_setup_and_navigation():
        """Transaction 1: Application page request and initial setup"""
        # Step 1: Navigate to the URL
        test.navigate_to_url()

        # Step 2: Wait for the page to load
        test.wait_for_page_load()

        # Step 3: Locate the shadow host element
        shadow_host = test.locate_shadow_host()

        # Step 4: Access the shadow root
        test.shadow_root = test.access_shadow_root(shadow_host)

        # Step 5: Take screenshot before login
        test.take_screenshot("Transaction_1_Application_Setup_Complete")

        return True

    check_and_log(
        test.driver,
        transaction_1_setup_and_navigation,
        "Transaction 1: Application page request and initial setup",
        measure_time=True,
        unique_str="transaction1",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_2
    #! Une fenêtre d'authentification s'affiche
    #! Saisir les champs suivants :
    #! Identifiant 	LOGIN Utilisateur S635525
    #! Mot de passe 	Password Utilisateur 0S635525
    #! Cliquer sur le bouton « Me connecter »
    # Transaction 2: Authentication process
    def transaction_2_authentication():
        """Transaction 2: Authentication window display and login process"""
        # Step 6: Locate username field
        username_field = test.locate_username_field()

        # Step 7: Enter username
        username_field.send_keys(username)

        # Step 8: Locate password field
        password_field = test.locate_password_field()

        # Step 9: Enter password
        password_field.send_keys(password)

        # Step 10: Locate login button
        login_button = test.locate_login_button()

        # Step 11: Click login button
        login_button.click()

        # Step 12: Take screenshot after successful login
        test.take_screenshot("Transaction_2_Authentication_Complete")

        return True

    check_and_log(
        test.driver,
        transaction_2_authentication,
        "Transaction 2: Authentication window display and login process",
        measure_time=True,
        unique_str="transaction2",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_3
    #! Le menu d'accueil d'Espace gestionnaire s'affiche.
    # Transaction 3: Main menu display verification
    def transaction_3_main_menu_display():
        """Transaction 3: Verify main menu of Espace gestionnaire is displayed"""
        # Step 11: Verify if '#btns-spaces > h3' exists
        test.locate_espace_gestionnaire_menu()

        # Step 12: Take screenshot
        test.take_screenshot("Transaction_3_Main_Menu_Display")

        return True

    check_and_log(
        test.driver,
        transaction_3_main_menu_display,
        "Transaction 3: Main menu of Espace gestionnaire display verification",
        measure_time=True,
        unique_str="transaction3",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_4
    #! Dans la liste déroulante du menu, sélectionner le menu
    #! « Gestion administrative/Paie », puis sélectionner le menu
    #! « Ressources humaines » ensuite sélectionner le menu « Collaborateur »
    #! Cliquer sur le menu « Détail contrat de travail »
    # Transaction 4: Navigation to contract details menu
    def transaction_4_menu_navigation():
        """Transaction 4: Navigate through menu system to reach contract details"""
        # Step 13: Click burger menu button
        test.click_burger_menu_button()

        # Step 14: Click Gestion administrative/Paie link
        test.click_gestion_administrative_paie_link()

        # Step 15: Click nth child 1 span (Ressources humaines)
        test.click_nth_child_1_span()

        # Step 16: Click Contrat link
        test.click_contrat_link()

        # Step 17: Click Detail contrat de travail
        test.click_detail_contrat_travail()

        return True

    check_and_log(
        test.driver,
        transaction_4_menu_navigation,
        "Transaction 4: Navigation to contract details menu",
        measure_time=True,
        unique_str="transaction4",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_5
    #! La page de recherche du « Détail contrat de travail » s'affiche
    #! Dans la page de recherche, sélectionner les critères de recherche ci-dessous :
    #! Nom commence par : saisir « DUVAL »
    #! Cliquer sur le bouton « Rechercher »
    # Transaction 5: Search functionality for contract details
    def transaction_5_search_functionality():
        """Transaction 5: Search page display and search for DUVAL"""
        # Step 18: Switch to frame
        test.switch_to_frame()

        # Step 19: Locate and prepare name field
        name_field = test.locate_name_field()

        # Step 20: Enter name DUVAL with enhanced error handling
        def enter_name_safely():
            try:
                # Clear the field first
                name_field.clear()
                # Enter the text
                name_field.send_keys("DUVAL")
                logging.info("Successfully entered 'DUVAL' in name field")
            except Exception as e:
                logging.error(f"Failed to enter name: {e}")
                # Try alternative approach - re-locate and try again
                import time

                time.sleep(1)
                new_field = test.locate_name_field()
                new_field.clear()
                new_field.send_keys("DUVAL")
                logging.info("Successfully entered 'DUVAL' using alternative approach")

        enter_name_safely()

        # Step 21: Click search button
        test.click_search_button()

        return True

    check_and_log(
        test.driver,
        transaction_5_search_functionality,
        "Transaction 5: Search functionality for contract details",
        measure_time=True,
        unique_str="transaction5",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_6
    #! Les résultats de recherche s'affichent
    #! Cliquer sur la première ligne dans les résultats de la recherche
    #! La page de détail du contrat de travail du salarié s'affiche
    # Transaction 6: Search results display and selection
    def transaction_6_search_results():
        """Transaction 6: Display search results and select first result"""
        # Step 22: Click first result
        test.click_first_result()

        return True

    check_and_log(
        test.driver,
        transaction_6_search_results,
        "Transaction 6: Search results display and selection",
        measure_time=True,
        unique_str="transaction6",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_7
    #! Déconnexion
    #! Cliquer sur le bouton « Se déconnecter ».
    # Transaction 7: Logout process
    def transaction_7_logout():
        """Transaction 7: Logout from the application"""
        # Step 23: Switch to default content
        test.switch_to_default_content()

        # Step 24: Locate and click logout button
        logout_button = test.locate_logout_button()
        logout_button.click()

        return True

    check_and_log(
        test.driver,
        transaction_7_logout,
        "Transaction 7: Logout from the application",
        measure_time=True,
        unique_str="transaction7",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_8
    #! L'utilisateur est déconnecté de l'application
    #! La page d'accueil de l'application s'affiche
    # Transaction 8: Final verification after logout
    def transaction_8_final_verification():
        """Transaction 8: Final verification - user is logged out and home page is displayed"""
        # Step 25: Take final screenshot to verify logout and home page display
        test.take_screenshot("Transaction_8_Final_Verification_Logout_Complete")

        return True

    check_and_log(
        test.driver,
        transaction_8_final_verification,
        "Transaction 8: Final verification - user logged out and home page displayed",
        measure_time=True,
        unique_str="transaction8",
        total_steps=1,
        take_screenshot=True,
        test_automation_instance=test,
        is_last_step=True,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")

finally:
    # Cleanup
    test.teardown_driver()

import sys
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Spacer, Paragraph, Image
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Spacer, Paragraph, Image
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.pdfgen import canvas
from get_data import temps_nominale, month_av_perf_percentage

if len(sys.argv) != 5:
    # print("Usage: python3 scripts.py <argument1:client> <argument2:mois>")
    sys.exit(1)

scenario = sys.argv[1]
start_date = sys.argv[2]
end_date = sys.argv[3]
client_name = sys.argv[4]


# Define an absolute path for saving the PDF file
output_pdf_path = '/app/output/tables.pdf'

# Create a PDF document
doc = SimpleDocTemplate(output_pdf_path, pagesize=letter)

# Logo image path
logo_path = "sopra-hr-software-logo-vector.png"

# Sample data for the tables
data_table1 = [
    ["Statut PSG"],
    # This will be updated dynamically
    ["intervalles par dates:", "TODO 01/06/2023 - 01/07/2023"],
    ["Créneau horaire:", "TODO 08 - 20"],  # This will be updated dynamically
    ["Samedi:", "TODO Exclu"],  # This will be updated dynamically
    ["Dimanche:", "TODO Exclu"],  # This will be updated dynamically
    ["KPI", "Disponibilité, Performance"],
    # This will be updated dynamically
    ["Temps de réponse nominale:", temps_nominale(
        client_name, scenario)],
    # This will be updated dynamically
    ["Fréquence d'exécution:", "TODO 10 min"],
]

data_table2 = [
    ["Synthèse PSG"],
    ["", "Plage de Service Garantie", ""],
    ["Nom du Scénario", "Disponibilité (%)", "Performance (%)"],
    ["Scenario COVEA 1 : PROD 4YOU - Liste de comptes bancaires",
        str(month_av_perf_percentage(scenario, start_date, end_date)[0])+"%", str(month_av_perf_percentage(scenario, start_date, end_date)[(-1)]) + "%"],
]

# Define styles
styles = getSampleStyleSheet()
table_style1 = TableStyle([
    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#ffb057')),
    ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#ffffff')),
    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
    ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#ffffff')),
    ('INNERGRID', (0, 0), (-1, -1), 0.25, colors.black),
    ('BOX', (0, 0), (-1, -1), 0.25, colors.black),
])

table_style2 = TableStyle([
    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#ffb057')),
    ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#ffffff')),
    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
    ('BACKGROUND', (0, 1), (-1, 1), colors.HexColor('#b5b5b5')),
    ('TEXTCOLOR', (0, 1), (-1, 1), colors.HexColor('#ffffff')),
    ('SPAN', (1, 0), (2, 0)),  # Merge the cells in the first row
    ('INNERGRID', (0, 0), (-1, -1), 0.25, colors.black),
    ('BOX', (0, 0), (-1, -1), 0.25, colors.black),
])

# Create story elements
story = []
story.append(Spacer(1, -120))  # Adjust the spacing as needed

# Add logo at the top right corner
logo = Image(logo_path, width=200, height=200)
text = "Rapport de supervision de la performance client_name scénario_name"
data = [[logo, Paragraph(text)]]
t = Table(data)

# Apply the vertical alignment to the table cell
t.setStyle(TableStyle([('VALIGN', (0, 0), (-1, -1), 'MIDDLE')]))

story.append(t)
# Spacer to separate the text from the content
story.append(Spacer(1, 20))  # Adjust the spacing as needed

# Add a red background using a Canvas
c = canvas.Canvas("tables.pdf")
c.setFillColor(colors.HexColor('#FF0000'))  # Red color
c.rect(0, 0, letter[0], letter[1], fill=True, stroke=False)
c.save()

# Add a red line break using a colored paragraph
red_line_break = Paragraph(
    '<font color="black" size="21">______________________________________</font>', styles["Normal"])
story.append(red_line_break)
story.append(Spacer(1, 12))  # Spacer between tables

title_style = styles["Heading2"]
title = Paragraph("Statut PSG", title_style)
story.append(title)

table1 = Table(data_table1, colWidths=[200, 200])
table1.setStyle(table_style1)
story.append(table1)
story.append(Spacer(1, 12))  # Spacer between tables
red_line_break = Paragraph(
    '<font color="black" size="21">______________________________________</font>', styles["Normal"])
story.append(red_line_break)
story.append(Spacer(1, 12))  # Spacer between tables


title_style = styles["Heading2"]
title = Paragraph("Synthèse PSG", title_style)
story.append(title)

table2 = Table(data_table2, colWidths=[300, 100, 100])
table2.setStyle(table_style2)
story.append(table2)


# Build the PDF document
doc.build(story)

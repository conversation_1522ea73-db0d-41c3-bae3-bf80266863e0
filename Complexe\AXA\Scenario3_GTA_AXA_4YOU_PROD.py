#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["axa"]["username"], credentials["axa"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-adp-axa.soprahronline.sopra/app/foryou/#/login"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    exit(1)

# Load credentials
username, password = load_credentials()

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:

    test.setup_driver()

    #! transaction_1
    #! Demander la page de l’application
    #! Une page d’accueil s’affiche
    # Step 1, 2, 3, 4 grouped together under "steps1234"
    # Step 1: Navigate to the URL
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to the URL",
        measure_time=True,
        unique_str="step1234",
        total_steps=4,  # Define total steps in the group
    )
    # Step 2: Wait for the page to load
    check_and_log(
        test.driver,
        test.wait_for_page_load,
        "Step 2: Wait for the page to load",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 3: Locate the shadow host element
    shadow_host = check_and_log(
        test.driver,
        test.locate_shadow_host,
        "Step 3: Locate shadow host",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 4: Access the shadow root
    test.shadow_root = check_and_log(
        test.driver,
        lambda: test.access_shadow_root(shadow_host),
        "Step 4: Access shadow root",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 5: Take screenshot before login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 5: Screenshot before login"),
        "Step 5: Take screenshot before login",
        measure_time=True,
        unique_str="step5",
        total_steps=1,
    )

    #! transaction_2
    #! Une fenêtre d’authentification s'affiche
    #! Saisir les champs suivants :
    #! Identifiant 	LOGIN Utilisateur S635522
    #! Mot de passe 	Password Utilisateur 0S635522
    #! Cliquer sur le bouton « Me connecter »
    # Step 6, 7, 8, 9, 10, 11 grouped together under "step6789101"
    # Step 6: Locate username field
    username_field = check_and_log(
        test.driver,
        test.locate_username_field,
        "Step 6: Locate username field",
        unique_str="step6789101",
        measure_time=True,
        total_steps=6,
    )

    # Step 7: Enter username
    check_and_log(
        test.driver,
        lambda: username_field.send_keys(username),
        "Step 7: Enter username",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 8: Locate password field
    password_field = check_and_log(
        test.driver,
        test.locate_password_field,
        "Step 8: Locate password field",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 9: Enter password
    check_and_log(
        test.driver,
        lambda: password_field.send_keys(password),
        "Step 9: Enter password",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 10: Locate login button
    login_button = check_and_log(
        test.driver,
        test.locate_login_button,
        "Step 10: Locate login button",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 11: Click login button
    check_and_log(
        test.driver,
        lambda: login_button.click(),
        "Step 11: Click login button",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 12: Take screenshot after successful login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 12: Screenshot after successful login"),
        "Step 12: Take screenshot after successful login",
        measure_time=True,
        unique_str="step12",
        total_steps=1,
    )

    #! transaction_3
    #! Le menu d’accueil d’Espace gestionnaire s’affiche.
    # Step 13, 14 grouped together under "step1314"

    # Step 13: transaction_3: Le menu d’accueil d’Espace gestionnaire s’affiche, Verify if '#btns-spaces > h3' exists
    check_and_log(
        test.driver,
        lambda: test.locate_espace_gestionnaire_menu,
        "Step 13: Verify if '#btns-spaces > h3' exists",
        measure_time=True,
        unique_str="step1314",
        total_steps=2,
    )

    # Step 14: Take screenshot of main menu
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 14: Screenshot of main menu"),
        "Step 14: Take screenshot of main menu",
        measure_time=True,
        unique_str="step1314",
    )

    #! transaction_4
    #! Dans la liste déroulante du menu,
    #! sélectionner le menu « Gestion administrative/Paie »,
    #! puis sélectionner le menu « Temps et activité »
    #! Cliquer sur le menu « suivi des temps »

    # Step 13: Locate burger menu button

    check_and_log(
        test.driver,
        test.locate_burger_menu_button,
        "Step 13: Locate burger menu button",
        measure_time=True,
    )
    check_and_log(
        test.driver,
        test.locate_burger_menu_button,
        "Step 13: Locate burger menu button",
        measure_time=True,
    )

    check_and_log(
        test.driver,
        test.click_burger_menu_button,
        "Step 13: Locate burger menu button",
        measure_time=True,
    )

    # Step 14: Locate mainMenuList_GestionadministrativePaie
    check_and_log(
        test.driver,
        test.locate_gestion_administrative_paie_link,
        "Step 14: Locate the 'Gestion administrative/Paie' link",
        measure_time=True,
    )

    check_and_log(
        test.driver,
        test.click_gestion_administrative_paie_link,
        "Step 14: Click the 'Gestion administrative/Paie' link.",
        measure_time=True,
    )

    # Step 15: Locate Temps et activités link
    check_and_log(
        test.driver,
        test.locate_link_text_temps_activite,
        "Step 15: Locate the 'Temps et activités' link",
        measure_time=True,
    )

    check_and_log(
        test.driver,
        test.click_link_text_temps_activite,
        "Step 15: Click the 'Temps et activités' link.",
        measure_time=True,
    )

    # Step 16: Locate Suivi des temps link
    check_and_log(
        test.driver,
        test.locate_link_text_suivi_des_temps,
        "Step 16: Locate the 'Suivi des temps' link",
        measure_time=True,
    )

    check_and_log(
        test.driver,
        test.click_link_text_suivi_des_temps,
        "Step 16: Click the 'Suivi des temps' link.",
        measure_time=True,
    )

    # step 17: Localte span white 2
    check_and_log(
        test.driver,
        test.locate_span_white_2,
        "Step 17: Locate the 'span white 2' link",
        measure_time=True,
    )

    # Step 16: Take screenshot
    check_and_log(
        test.driver,
        test.take_screenshot,
        "Step 15: Take screenshot",
        measure_time=True,
    )

    #! transaction_5
    #! Cliquer sur l’onglet « AXA France » pour ouvrir l’arbre des structures

    #! transaction_4
    #! Déconnexion
    #! Cliquer sur le bouton « Déconnexion »
    # Step 13, 14 grouped together under "step1314"
    # Step 13: Locate logout button
    logout_button = check_and_log(
        test.driver,
        test.locate_logout_button,
        "Step 13: Locate logout button",
        measure_time=True,
        unique_str="step1314",
        total_steps=2,
    )

    # Step 14: Click logout button
    check_and_log(
        test.driver,
        lambda: logout_button.click(),
        "Step 14: Click logout button",
        measure_time=True,
        unique_str="step1314",
    )

    #! transaction_5
    #! L’utilisateur est déconnecté de l’application
    #! La page d’accueil de l’application s’affiche
    # Step 15: Take screenshot
    check_and_log(
        test.driver,
        test.take_screenshot,
        "Step 15: Take screenshot",
        measure_time=True,
        unique_str="step15",
        total_steps=1,
        is_last_step=True,
    )


except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")

finally:
    # Cleanup
    test.teardown_driver()

#!/bin/bash

set -e

echo "Executing clone_main_table.py to ensure database table exists..."
python3 clone_main_table.py

if [ "$SCRIPTNAME" = "multi_launch.py" ]; then
    if [ -z "$CLIENT_NAME" ]; then
        echo "Error: CLIENT_NAME is required."
        echo "Usage: SCRIPTNAME=multi_launch.py CLIENT_NAME=<NAME>"
        exit 1
    fi
    echo "Executing multi_launch.py with client: $CLIENT_NAME"
    python3 multi_launch.py "$CLIENT_NAME"

elif [ "$SCRIPTNAME" = "multi_app.py" ]; then
    echo "Executing multi_app.py"
    python3 multi_app.py

else
    echo "No script specified or invalid script name."
    echo "Please set SCRIPTNAME and (for multi_launch) CLIENT_NAME."
    exit 1
fi

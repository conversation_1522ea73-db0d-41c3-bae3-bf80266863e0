#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
    output_failure_results,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["lcl"]["username"], credentials["lcl"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://lcl.soprahronline.sopra/hra-space/?nosso"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Load credentials
try:
    username, password = load_credentials()
except Exception as e:
    logging.error(f"Failed to load credentials: {e}")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:

    test.setup_driver()

    #! transaction_1
    #! Demander la page de l'application
    #! Une page d'accueil s'affiche
    # Step 1, 2 grouped together under "step12"
    # Step 1: Navigate to the URL
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to the URL",
        measure_time=True,
        unique_str="step1",
        total_steps=1,
    )

    # Step 2: Take screenshot before login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 2: Screenshot before login"),
        "Step 2: Take screenshot before login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_2
    #! Une fenêtre d'authentification s'affiche
    #! Saisir les champs suivants :
    #! Identifiant 	LOGIN Utilisateur HYPERIC
    #! Mot de passe 	Password Utilisateur RATEAU75
    #! Cliquer sur le bouton « Me connecter »
    # Step 3-5: Complete LCL login process with validation
    check_and_log(
        test.driver,
        lambda: test.lcl_direct_login(username, password),
        "Step 3-5: LCL login process with validation",
        measure_time=True,
        unique_str="step345",
        total_steps=1,
    )

    # Step 6: Take screenshot after successful login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 6: Screenshot after successful login"),
        "Step 6: Take screenshot after successful login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
    )

    #! transaction_3
    #! Déconnexion
    #! Cliquer sur le bouton « Déconnexion »

    # Step 7, 8 grouped together under "step78"
    # Step 7: Locate logout button
    logout_button = check_and_log(
        test.driver,
        test.lcl_locate_logout_button,
        "Step 7: Locate logout button",
        measure_time=True,
        unique_str="step78",
        total_steps=2,
    )

    # Step 8: Click logout button
    check_and_log(
        test.driver,
        lambda: logout_button.click(),
        "Step 8: Click logout button",
        measure_time=True,
        unique_str="step78",
    )
    #! transaction_4
    #! L'utilisateur est déconnecté de l'application
    #! La page d'accueil de l'application s'affiche
    # Step 9: Take final screenshot
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 9: Final screenshot after logout"),
        "Step 9: Final screenshot after logout",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        is_last_step=True,
    )


except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")
    # Output failure results to stdout
    output_failure_results()

finally:
    # Cleanup
    test.teardown_driver()


1. build
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080

cd /data2/dockerized_rpa_v2/matplotlib_graphs/BGM
podman build -t bgm-automated-cmplx .

2. running in general:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/bgm3/pdfs:/app/output bgm-automated-cmplx

3. running for may:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/bgm3/pdfs:/app/output bgm-automated-cmplx "HR - scénario PERF bgm3" "2023-05-01" "2023-05-31" "bgm3"

4. running for august:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/bgm3/pdfs:/app/output bgm-automated-cmplx "HR - scénario PERF bgm3" "2023-08-01" "2023-08-31" "bgm3"

sh run_docker_scenarios.sh

podman run bgm-automated-cmplx


ssh admrpa@frsopslapp065 "docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/bgm3/pdfs:/app/output bgm-automated-cmplx"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/bgm3/pdfs:/app/output bgm-automated-cmplx


docker run -it bgm-automated-cmplx /bin/bash
podman exec -it  bgm-automated-cmplx /bin/bash

podman run --rm bgm-automated-cmplx



podman save -o bgm-automated-cmplx.tar bgm-automated-cmplx
scp bgm-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/bgm-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913

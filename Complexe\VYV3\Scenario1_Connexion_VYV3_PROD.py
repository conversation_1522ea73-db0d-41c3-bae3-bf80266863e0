#!/usr/bin/env python3
"""
VYV3 Scenario 1: Connexion VYV3 PROD
This scenario logs into VYV3 HRA-Space portal, performs authentication with module selection,
and validates successful login/logout functionality.
"""
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
    output_failure_results,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return (
            credentials["vyv3"]["username"],
            credentials["vyv3"]["password"],
            credentials["vyv3"]["login_module"]
        )
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://hsm.soprahronline.sopra/hra-space/portal"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Load credentials
try:
    username, password, login_module = load_credentials()
except Exception as e:
    logging.error(f"Failed to load credentials: {e}")
    # Reset scenario variables and output failure results
    reset_scenario_variables()
    output_failure_results()
    exit(1)

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:
    test.setup_driver()

    #! transaction_1: Navigate to VYV3 HRA-Space portal and complete login
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to VYV3 HRA-Space portal",
        measure_time=True,
        unique_str="transaction1",
        total_steps=6,
    )

    # Step 2: Locate and enter username
    username_field = check_and_log(
        test.driver,
        test.vyv3_locate_username_field,
        "Step 2: Locate username field",
        measure_time=True,
        unique_str="transaction1",
    )

    check_and_log(
        test.driver,
        lambda: username_field.send_keys(username),
        "Step 3: Enter username",
        measure_time=True,
        unique_str="transaction1",
    )

    # Step 4: Locate and enter password
    password_field = check_and_log(
        test.driver,
        test.vyv3_locate_password_field,
        "Step 4: Locate password field",
        measure_time=True,
        unique_str="transaction1",
    )

    check_and_log(
        test.driver,
        lambda: password_field.send_keys(password),
        "Step 5: Enter password",
        measure_time=True,
        unique_str="transaction1",
    )

    # Step 6: Select login module
    check_and_log(
        test.driver,
        lambda: test.vyv3_select_login_module(login_module),
        f"Step 6: Select login module ({login_module})",
        measure_time=True,
        unique_str="transaction1",
    )

    #! transaction_2: Complete authentication and validate login
    check_and_log(
        test.driver,
        test.vyv3_click_login_button,
        "Step 7: Click login button",
        measure_time=True,
        unique_str="transaction2",
        total_steps=3,
    )

    # Step 8: Take screenshot after successful login
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Step 8: Screenshot after successful login"),
        "Step 8: Take screenshot after successful login",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        unique_str="transaction2",
    )

    # Step 9: Logout from VYV3 application
    check_and_log(
        test.driver,
        test.vyv3_click_logout_button,
        "Step 9: Logout from VYV3 application",
        measure_time=True,
        unique_str="transaction2",
        is_last_step=True,
    )

except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")
    # Output failure results to stdout
    output_failure_results()

finally:
    # Cleanup
    test.teardown_driver()

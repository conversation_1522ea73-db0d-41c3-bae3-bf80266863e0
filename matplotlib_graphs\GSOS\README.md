
1. build
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080
cd /data2/dockerized_rpa_v2/matplotlib_graphs/GSOS
podman build -t gsos-automated-cmplx .

2. running in general:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/GSOS/pdfs:/app/output gsos-automated-cmplx

3. running for may:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/GSOS/pdfs:/app/output gsos-automated-cmplx "HR - scénario PERF GSOS" "2023-05-01" "2023-05-31" "GSOS"

4. running for august:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/GSOS/pdfs:/app/output gsos-automated-cmplx "HR - scénario PERF GSOS" "2023-10-01" "2023-10-31" "GSOS"

sh run_docker_scenarios.sh

podman run gsos-automated-cmplx


ssh admrpa@frsopslapp065 "docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/GSOS/pdfs:/app/output gsos-automated-cmplx"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/GSOS/pdfs:/app/output gsos-automated-cmplx


docker run -it gsos-automated-cmplx /bin/bash
podman exec -it  gsos-automated-cmplx /bin/bash

podman run --rm gsos-automated-cmplx



podman save -o gsos-automated-cmplx.tar gsos-automated-cmplx
scp gsos-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/gsos-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913


daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR'   
                AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(32, 42)]
temps_reponse_nominal:
 42
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 1 : Connexion COLLABORATEUR
selected start_date:
 2024-03-01
selected end_date:
 2024-03-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP'   
                AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(63, 79)]
temps_reponse_nominal:
 79
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 2 : Fiche de synthèse ADP
selected start_date:
 2024-03-01
selected end_date:
 2024-03-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 3 : GTA'   
                AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 3 : GTA'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(69, 89)]
temps_reponse_nominal:
 89
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 3 : GTA'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 3 : GTA'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 3 : GTA
selected start_date:
 2024-03-01
selected end_date:
 2024-03-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail'   
                AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(63, 79)]
temps_reponse_nominal:
 79
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 4 : Détails contrat de travail
selected start_date:
 2024-03-01
selected end_date:
 2024-03-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 5 : Portail de services'   
                AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 5 : Portail de services'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(38, 50)]
temps_reponse_nominal:
 50
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 5 : Portail de services'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 5 : Portail de services'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 5 : Portail de services
selected start_date:
 2024-03-01
selected end_date:
 2024-03-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités'   
                AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(36, 50)]
temps_reponse_nominal:
 50
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 6 : Corbeille d’activités
selected start_date:
 2024-03-01
selected end_date:
 2024-03-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel'   
                AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(62, 50)]
temps_reponse_nominal:
 50
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 7 : Consultation du planning mensuel
selected start_date:
 2024-03-01
selected end_date:
 2024-03-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py
daily_graph_data Query: SELECT
                DATE(kdc.date_step) AS per_day,
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME,
                SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                SUM(
                CASE
                    WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                    WHEN kdc.transaction_status = 1 THEN 1
                    ELSE 0
                END
            ) AS nbupslatime,
                SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                ROUND(AVG(kdc.duration),
                2) AS moyrestime
            FROM
                khronos_data_containers_v2 kdc,
                cust_perf_features cpf
            WHERE
                kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique'   
                AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                AND cpf.SCENARIOID = kdc.scenario_id
            GROUP BY
                DATE(kdc.date_step);
Executed Query: SELECT
                cpf.NOMINALTIME,
                cpf.ACCEPTABLETIME
            FROM
                khronos_scenario_step kss
            JOIN khronos_scenario_name ksn ON
                kss.scenario = ksn.id
            JOIN cust_perf_features cpf ON
                ksn.id = cpf.SCENARIOID
            JOIN cust_config_web ccw1 ON
                cpf.CUSTCONFWEBID = ccw1.ID
            JOIN clients_conf cc1 ON
                ccw1.CLIENTID = cc1.CLIENTID
                AND ccw1.CLIENTCONFID = cc1.ID
                AND cc1.CLIENTNAME LIKE '%AXAE5%'
                AND cc1.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ksn.name like 'PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique'
                AND ccw1.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            JOIN cust_config_web ccw2 ON
                cpf.CUSTCONFID = ccw2.CLIENTCONFID 
            JOIN clients_conf cc2 ON
                ccw2.CLIENTID = cc2.CLIENTID
                AND ccw2.CLIENTCONFID = cc2.ID
                AND cc2.CLIENTNAME LIKE '%AXAE5%'
                AND cc2.CUSTCONFLABEL LIKE 'AXAE5-PROD'
                AND ccw2.CONTEXT_ROOT_ACC LIKE '%/app/foryou/%'
            GROUP BY
                cpf.NOMINALTIME,
                cpf.SCENARIOID
records:
 [(62, 78)]
temps_reponse_nominal:
 78
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
month_av_perf_percentage Query: SELECT
                    MONTH(kdc.date_step) AS month_number,
                    cpf.NOMINALTIME,
                    cpf.ACCEPTABLETIME,
                    SUM(CASE WHEN kdc.transaction_status = 1 THEN 1 ELSE 0 END) AS nbupslaav,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslaav,
                    SUM(
                                    CASE
                                        WHEN kdc.transaction_status = 1 AND kdc.duration BETWEEN 0 AND cpf.ACCEPTABLETIME THEN 1
                                        WHEN kdc.transaction_status = 1 THEN 1
                                        ELSE 0
                                    END
                                ) AS nbupslatime,
                    SUM(CASE WHEN kdc.transaction_status <= 1 THEN 1 ELSE 0 END) AS nballslatime,
                    ROUND(AVG(kdc.duration),
                                    2) AS moyrestime
                FROM
                    khronos_data_containers_v2 kdc,
                    cust_perf_features cpf
                WHERE
                    kdc.scenario_name LIKE 'PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique'   
                    AND kdc.date_step BETWEEN '2024-03-01' AND '2024-03-31'
                    AND cpf.SCENARIOID = kdc.scenario_id
                GROUP BY
                    MONTH(kdc.date_step)
Current Directory: /app
selected scenario:
 PL - AXA_4YOU_PROD - Scénario 8 : Consultation du dossier numérique
selected start_date:
 2024-03-01
selected end_date:
 2024-03-31
selected client_name:
 AXAE5
running report_graphs.py
running report_tables.py

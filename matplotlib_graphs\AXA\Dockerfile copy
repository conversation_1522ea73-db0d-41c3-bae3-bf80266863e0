# Use the Oracle Linux 8 base image
FROM oraclelinux:8

# Install required packages
RUN dnf install -y gcc-c++ libjpeg-turbo-devel make
RUN dnf install -y python36
RUN dnf install -y python3-pip
RUN python3.6 -m pip install --upgrade pip

# Install required Python packages
RUN pip3.6 install matplotlib==3.3 numpy==1.15.0 mysql-connector-python

# Install font-related dependencies
RUN dnf install -y freetype freetype-devel libX11 libX11-devel libXtst libXtst-devel

# Install PyPDF2 and reportlab from the downloaded .whl files
RUN pip3.6 install reportlab

# Create a directory for the script
WORKDIR /app

# Copy the script file into the container
COPY . /app/

# Create an output directory in the container
RUN mkdir /app/output

# Command to execute the script
ENTRYPOINT ["python3", "get_data.py"]
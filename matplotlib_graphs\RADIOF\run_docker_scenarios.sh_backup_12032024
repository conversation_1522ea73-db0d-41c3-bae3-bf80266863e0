#!/bin/bash

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 1 : Mise en paiement collective des cachetiers et pigistes" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 2 : Coordonnées" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 3 : Gestion des demandes" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 4 : Affectations" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 5 : Temps de travail" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 6 : Transport" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 7 : Primes" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 8 : Données bancaires"  "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 9 : Rémunération" "2023-08-01" "2023-08-31" "RADIOF"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/RADIOF/pdfs:/app/output radiof-automated-cmplx "PL - RADIOFRANCE - Scénario 10 : Fiche de synthèse" "2023-08-01" "2023-08-31" "RADIOF"


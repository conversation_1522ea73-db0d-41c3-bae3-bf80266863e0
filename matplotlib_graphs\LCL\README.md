
1. build
export http_proxy=http://frsopslinfv01.soprahronline.sopra:8080 && export https_proxy=http://frsopslinfv01.soprahronline.sopra:8080
cd /data2/dockerized_rpa_v2/matplotlib_graphs/LCL
podman build -t lcl-automated-cmplx .

2. running in general:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/LCL/pdfs:/app/output lcl-automated-cmplx

3. running for may:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/LCL/pdfs:/app/output lcl-automated-cmplx "HR - scénario PERF LCL" "2023-05-01" "2023-05-31" "LCL"

4. running for august:
docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/LCL/pdfs:/app/output lcl-automated-cmplx "HR - scénario PERF LCL" "2023-10-01" "2023-10-31" "LCL"


podman run lcl-automated-cmplx


ssh admrpa@frsopslapp065 "docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/LCL/pdfs:/app/output lcl-automated-cmplx"


podman system prune
podman image prune

podman run -v /data2/dockerized_rpa_v2/matplotlib_graphs/LCL/pdfs:/app/output lcl-automated-cmplx


docker run -it lcl-automated-cmplx /bin/bash
podman exec -it  lcl-automated-cmplx /bin/bash

podman run --rm lcl-automated-cmplx



podman save -o lcl-automated-cmplx.tar lcl-automated-cmplx
scp lcl-automated-cmplx.tar admjenkins@frsopslappv55:/tmp
docker load -i /tmp/lcl-automated-cmplx.tar
docker images
docker run -v /tmp:/app/output e79df6561913

import sys
import matplotlib.backends.backend_pdf
import matplotlib.pyplot as plt
import numpy as np
from get_data import weekly_graph_data, daily_graph_data

if len(sys.argv) != 5:
    # print("Usage: python3 scripts.py <argument1:client> <argument2:mois>")
    sys.exit(1)

scenario = sys.argv[1]
start_date = sys.argv[2]
month_names = {
    '01': 'janvier',
    '02': 'février',
    '03': 'mars',
    '04': 'avril',
    '05': 'mai',
    '06': 'juin',
    '07': 'juillet',
    '08': 'août',
    '09': 'septembre',
    '10': 'octobre',
    '11': 'novembre',
    '12': 'décembre'
}
month = start_date.split("-")[1] 
# Get the French month name using the dictionary
french_month_name = month_names.get(month)
end_date = sys.argv[3]
client_name = sys.argv[4]

weekly_data = weekly_graph_data(scenario, start_date, end_date)
daily_data = daily_graph_data(scenario, start_date, end_date)

days_numbers = [daily_data[i][0] for i in range(len(daily_data))]
# print("days_numbers: ", days_numbers)

daily_av_percentage_values = [
    float(daily_data[i][3]) / float(daily_data[i][4]) * 100
    for i in range(len(daily_data))
]
# print("daily_av_percentage_values: {}".format(daily_av_percentage_values))

daily_perf_percentage_values = [
    round(float(daily_data[i][5]) / float(daily_data[i][6]) * 100, 2)
    for i in range(len(daily_data))
]
# print("daily_perf_percentage_values: {}".format(daily_perf_percentage_values))

daily_graph_plot_data_av = {
    days_numbers[i]: daily_av_percentage_values[i]
    for i in range(len(days_numbers))
}
# print("daily_graph_plot_data_av: {}".format(daily_graph_plot_data_av))


daily_graph_plot_data_perf = {
    days_numbers[i]: daily_perf_percentage_values[i]
    for i in range(len(days_numbers))
}
# print("daily_graph_plot_data_perf: {}".format(daily_graph_plot_data_perf))

week_numbers = [weekly_data[i][0] for i in range(len(weekly_data))]
# print("week numbers: {}".format(week_numbers))
av_percentage_values = [
    float(weekly_data[i][3]) / float(weekly_data[i][4]) * 100
    for i in range(len(weekly_data))
]
# print("percentage_values: {}".format(percentage_values))

perf_percentage_values = [
    round(float(weekly_data[i][5]) / float(weekly_data[i][6]) * 100, 2)
    for i in range(len(weekly_data))
]

# print("perf_percentage_values:", perf_percentage_values)

weekly_graph_plot_data_perf = {
    week_numbers[i]: perf_percentage_values[i]
    for i in range(len(week_numbers))
}
# print("weekly_graph_plot_data_perf: {}".format(weekly_graph_plot_data_perf))

weekly_graph_plot_data_av = {
    week_numbers[i]: av_percentage_values[i]
    for i in range(len(week_numbers))
}
# print("weekly_graph_plot_data_av: {}".format(weekly_graph_plot_data_av))

# Create some example data
x = np.linspace(0, 10, 100)
days_in_may = np.arange(1, 32)  # Days of May
y_percentages = np.random.uniform(
    0, 100, size=(31, 4))  # 31 days and 4 histograms

y_percentages = np.random.uniform(
    0, 100, size=(31, 4))  # 31 days and 4 histograms

# Calculate the week numbers for each day
week_numbers = np.array([date // 7 + 1 for date in days_in_may])

# Create a new PDF
# pdf = matplotlib.backends.backend_pdf.PdfPages('graphs.pdf')
# Create a new PDF with an absolute path
pdf = matplotlib.backends.backend_pdf.PdfPages('/app/output/graphs.pdf')

# Create a new figure for Page 1
fig_hist = plt.figure(figsize=(10, 8))

# Create a 2x2 grid of subplots
grid_spec_hist = fig_hist.add_gridspec(2, 2)

# Add histograms to each subplot
hist_ax1 = fig_hist.add_subplot(grid_spec_hist[0, 0])
week_numbers_unique = np.unique(week_numbers)
week_avg_percentages = [np.mean(
    y_percentages[week_numbers == week], axis=0) for week in week_numbers_unique]

for k, v in weekly_graph_plot_data_av.items():
    hist_ax1.bar(
        k, v, color='#d41524',
        edgecolor='white', label=f"Week {k}"
    )

hist_ax1.set_title('Évolution de Disponibilité par semaine:')
hist_ax1.set_xticks([weekly_data[i][0] for i in range(len(weekly_data))])
hist_ax1.set_xticklabels(
    [f"Week {week}" for week in [weekly_data[i][0] for i in range(len(weekly_data))]], rotation='vertical')
hist_ax1.set_xlabel(f'Semaines de {french_month_name}')
hist_ax1.set_ylabel('Pourcentage')

hist_ax2 = fig_hist.add_subplot(grid_spec_hist[0, 1])


for k, v in weekly_graph_plot_data_perf.items():
    hist_ax2.bar(
        k, v, color='orange',
        edgecolor='white', label=f"Week {k}"
    )

hist_ax2.set_title('Évolution de Performance par semaine:')
hist_ax2.set_xticks([weekly_data[i][0] for i in range(len(weekly_data))])
hist_ax2.set_xticklabels(
    [f"Week {week}" for week in [weekly_data[i][0] for i in range(len(weekly_data))]], rotation='vertical')
hist_ax2.set_xlabel(f'Semaines de {french_month_name}')
hist_ax2.set_ylabel('Pourcentage')

# Replace hist_ax3 with a line chart
line_ax3 = fig_hist.add_subplot(grid_spec_hist[1, 0])
line_ax3.plot(
    days_numbers, daily_av_percentage_values, color='#dab6b9',
    marker='o', markerfacecolor='red', label="Disponibilité"
)

# Annotate values above the markers with 45-degree rotation
for day, value in zip(days_numbers, daily_av_percentage_values):
    line_ax3.annotate(f'{value:.2f}', (day, value), textcoords="offset points", xytext=(
        0, 10), ha='center', rotation='vertical')


line_ax3.set_title('Évolution de Disponibilité par jour')
line_ax3.set_xticks(days_numbers)
# Rotate labels vertically
line_ax3.set_xticklabels(
    [str(day) for day in days_numbers], rotation='vertical'
)
line_ax3.set_xlabel('Jours de Mai')
line_ax3.set_ylabel('Pourcentage')

# Replace hist_ax4 with a line chart
line_ax4 = fig_hist.add_subplot(grid_spec_hist[1, 1])
line_ax4.plot(
    days_numbers, daily_perf_percentage_values, color='#dab6b9',
    marker='o', markerfacecolor='red', label="Performance"
)
# Annotate values above the markers
for day, value in zip(days_numbers, daily_perf_percentage_values):
    line_ax4.annotate(f'{value:.2f}', (day, value),
                      textcoords="offset points", xytext=(0, 10), ha='center', rotation='vertical')

line_ax4.set_title('Évolution de Performance par jour')
line_ax4.set_xticks(days_numbers)
# Rotate labels vertically
line_ax4.set_xticklabels(
    [str(day) for day in days_numbers], rotation='vertical'
)
line_ax4.set_xlabel('Jours de Mai')
line_ax4.set_ylabel('Pourcentage')

# Adjust layout of the second page
plt.tight_layout()

# Save the figure on Page 1
pdf.savefig(fig_hist)
plt.close(fig_hist)

# Close the PDF
pdf.close()

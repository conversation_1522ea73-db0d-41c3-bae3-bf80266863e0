#!/usr/bin/env python3
import logging
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
)


def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")

        with open(credentials_path, "r") as f:
            credentials = json.load(f)

        return credentials["axa"]["username"], credentials["axa"]["password"]
    except FileNotFoundError:
        logging.error("credentials.json file not found")
        raise
    except KeyError as e:
        logging.error(f"Missing key in credentials.json: {e}")
        raise
    except json.JSONDecodeError:
        logging.error("Invalid JSON format in credentials.json")
        raise


# Set the URL
url = "https://ple5-adp-axa.soprahronline.sopra/app/foryou/#/login"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    exit(1)

# Load credentials
username, password = load_credentials()

# Reset scenario variables for clean start
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

# Setup the driver and perform actions
try:

    test.setup_driver()

    #! transaction_1
    #! Demander la page de l’application
    #! Une page d’accueil s’affiche
    # Step 1, 2, 3, 4 grouped together under "steps1234"
    # Step 1: Navigate to the URL
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to the URL",
        measure_time=True,
        unique_str="step1234",
        total_steps=5,  # Define total steps in the group (Steps 1-5)
    )
    # Step 2: Wait for the page to load
    check_and_log(
        test.driver,
        test.wait_for_page_load,
        "Step 2: Wait for the page to load",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 3: Locate the shadow host element
    shadow_host = check_and_log(
        test.driver,
        test.locate_shadow_host,
        "Step 3: Locate shadow host",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 4: Access the shadow root
    test.shadow_root = check_and_log(
        test.driver,
        lambda: test.access_shadow_root(shadow_host),
        "Step 4: Access shadow root",
        measure_time=True,
        unique_str="step1234",
    )

    # Step 5: Take screenshot after transaction 1 (page loaded)
    check_and_log(
        test.driver,
        lambda: test.take_screenshot(
            "Transaction 1 Complete: Page loaded and shadow DOM ready"
        ),
        "Step 5: Screenshot after transaction 1 - Page loaded",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        unique_str="step1234",
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_2
    #! Une fenêtre d’authentification s'affiche
    #! Saisir les champs suivants :
    #! Identifiant 	LOGIN Utilisateur S635522
    #! Mot de passe 	Password Utilisateur 0S635522
    #! Cliquer sur le bouton « Me connecter »
    # Step 6, 7, 8, 9, 10, 11 grouped together under "step6789101"
    # Step 6: Locate username field
    username_field = check_and_log(
        test.driver,
        test.locate_username_field,
        "Step 6: Locate username field",
        unique_str="step6789101",
        measure_time=True,
        total_steps=7,  # Steps 6-11 and Step 12 screenshot
    )

    # Step 7: Enter username
    check_and_log(
        test.driver,
        lambda: username_field.send_keys(username),
        "Step 7: Enter username",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 8: Locate password field
    password_field = check_and_log(
        test.driver,
        test.locate_password_field,
        "Step 8: Locate password field",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 9: Enter password
    check_and_log(
        test.driver,
        lambda: password_field.send_keys(password),
        "Step 9: Enter password",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 10: Locate login button
    login_button = check_and_log(
        test.driver,
        test.locate_login_button,
        "Step 10: Locate login button",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 11: Click login button
    check_and_log(
        test.driver,
        lambda: login_button.click(),
        "Step 11: Click login button",
        unique_str="step6789101",
        measure_time=True,
    )

    # Step 12: Take screenshot after transaction 2 (login complete)
    check_and_log(
        test.driver,
        lambda: test.take_screenshot("Transaction 2 Complete: Login successful"),
        "Step 12: Screenshot after transaction 2 - Login complete",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        unique_str="step6789101",
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_3
    #! Le menu d’accueil d’Espace gestionnaire s’affiche.
    # Step 13, 14 grouped together under "step1314"

    # Step 13: transaction_3: Le menu d’accueil d’Espace gestionnaire s’affiche, Verify if '#btns-spaces > h3' exists
    check_and_log(
        test.driver,
        lambda: test.locate_espace_gestionnaire_menu,
        "Step 13: Verify if '#btns-spaces > h3' exists",
        measure_time=True,
        unique_str="step1314",
        total_steps=3,  # Steps 13, 14, and screenshot
    )

    # Step 14: Take screenshot after transaction 3 (main menu verified)
    check_and_log(
        test.driver,
        lambda: test.take_screenshot(
            "Transaction 3 Complete: Espace gestionnaire menu verified"
        ),
        "Step 14: Screenshot after transaction 3 - Main menu verified",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        unique_str="step1314",
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_4
    #! Dans la liste déroulante du menu, sélectionner le menu
    #! « Gestion administrative/Paie », puis sélectionner le menu
    #! « Ressources humaines » ensuite sélectionner le menu « Collaborateur »
    #! Cliquer sur le menu « Fiche de synthèse du collaborateur »

    # Step 13: Locate burger menu button

    check_and_log(
        test.driver,
        test.locate_burger_menu_button,
        "Step 13: Locate burger menu button",
        measure_time=True,
        unique_str="step13to16",
        total_steps=11,  # All steps in Transaction 4
    )
    check_and_log(
        test.driver,
        test.locate_burger_menu_button,
        "Step 13: Locate burger menu button",
        measure_time=True,
        unique_str="step13to16",
    )

    check_and_log(
        test.driver,
        test.click_burger_menu_button,
        "Step 13: Click burger menu button",
        measure_time=True,
        unique_str="step13to16",
    )
    # check_and_log(
    #     test.driver,
    #     test.locate_burger_menu_button,
    #     "Step 13: Locate burger menu button",
    #     measure_time=True,
    # )
    # check_and_log(
    #     test.driver,
    #     test.locate_burger_menu_button,
    #     "Step 13: Locate burger menu button",
    #     measure_time=True,
    # )

    # Step 14: Locate mainMenuList_GestionadministrativePaie
    check_and_log(
        test.driver,
        test.locate_gestion_administrative_paie_link,
        "Step 14: Locate the 'Gestion administrative/Paie' link",
        measure_time=True,
        unique_str="step13to16",
    )

    check_and_log(
        test.driver,
        test.click_gestion_administrative_paie_link,
        "Step 14: Click the 'Gestion administrative/Paie' link.",
        measure_time=True,
        unique_str="step13to16",
    )

    # Step 15: Locate nth child 1 span
    check_and_log(
        test.driver,
        test.locate_nth_child_1_span,
        "Step 15: Locate the nth child 1 span.",
        measure_time=True,
        unique_str="step13to16",
    )

    check_and_log(
        test.driver,
        test.click_nth_child_1_span,
        "Step 15: Click the nth child 1 span.",
        measure_time=True,
        unique_str="step13to16",
    )

    # Step 16: Locate nth child 3 span
    check_and_log(
        test.driver,
        test.locate_nth_child_3_span,
        "Step 16: Locate the nth child 3 span.",
        measure_time=True,
        unique_str="step13to16",
    )

    check_and_log(
        test.driver,
        test.click_nth_child_3_span,
        "Step 16: Click the nth child 3 span.",
        measure_time=True,
        unique_str="step13to16",
    )

    check_and_log(
        test.driver,
        test.locate_nth_child_7_span,
        "Step 16: Locate the nth child 7 span.",
        measure_time=True,
        unique_str="step13to16",
    )

    check_and_log(
        test.driver,
        test.click_nth_child_7_span,
        "Step 16: Click the nth child 7 span.",
        measure_time=True,
        unique_str="step13to16",
    )

    # Step 16: Take screenshot after transaction 4 (navigation to Fiche de synthèse complete)
    check_and_log(
        test.driver,
        lambda: test.take_screenshot(
            "Transaction 4 Complete: Fiche de synthèse du collaborateur accessed"
        ),
        "Step 16: Screenshot after transaction 4 - Fiche de synthèse accessed",
        measure_time=False,  # Screenshots should not be counted in performance metrics
        unique_str="step13to16",
        take_screenshot=True,
        test_automation_instance=test,
    )

    #! transaction_5
    #! Déconnexion
    #! Cliquer sur le bouton « Déconnexion »
    # Step 13, 14 grouped together under "step1314"
    # Step 17: Locate logout button (Transaction 5)
    logout_button = check_and_log(
        test.driver,
        test.locate_logout_button,
        "Step 17: Locate logout button",
        measure_time=True,
        unique_str="step1718",
        total_steps=2,
    )

    # Step 18: Click logout button (Transaction 5)
    check_and_log(
        test.driver,
        lambda: logout_button.click(),
        "Step 18: Click logout button",
        measure_time=True,
        unique_str="step1718",
    )

    #! transaction_6
    #! L’utilisateur est déconnecté de l’application
    #! La page d’accueil de l’application s’affiche
    # Step 19: Take screenshot after transaction 6 (final verification)
    check_and_log(
        test.driver,
        lambda: test.take_screenshot(
            "Transaction 6 Complete: User logged out successfully"
        ),
        "Step 19: Screenshot after transaction 6 - Final verification",
        measure_time=True,  # This should be measured as Transaction 6
        unique_str="step19",
        total_steps=1,
        is_last_step=True,
    )


except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")

finally:
    # Cleanup
    test.teardown_driver()

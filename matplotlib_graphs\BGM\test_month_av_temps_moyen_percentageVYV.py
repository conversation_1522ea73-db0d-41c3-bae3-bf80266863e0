import mysql.connector

scenario = "HR - scénario PERF VYV3"
start_date = "2024-03-01"
end_date = "2024-03-31"

#! test VYV
config2 = {
    "user": "prodops",
    "password": "prodops",
    "host": "*********",
    "port": "3306",
    "database": "khronos",
    "raise_on_warnings": True,
}

def month_av_temps_moyen_percentage(scenario, start_date, end_date):
    connection = mysql.connector.connect(**config2)
    cursor = connection.cursor()
    sql_query = ""
    sql_query = """
                SELECT 
                    ROUND(AVG(kdc.duration), 2) AS temps_moyen
                FROM khronos_data_containers_v2 kdc
                        JOIN cust_perf_features cpf ON cpf.SCENARIOID = kdc.scenario_id
                WHERE kdc.scenario_name LIKE %s 
                    AND kdc.date_step BETWEEN %s AND %s
                """
    # Pass the scenario as a parameter to execute                    
    cursor.execute(sql_query, (scenario, start_date, end_date))  
    records = cursor.fetchall()
    temps_moyen = records[-1][0]
    query_with_params = cursor.statement    
    print("month_av_temps_moyen_percentage Query:", query_with_params)        
    cursor.close()
    connection.close()
    return temps_moyen
    
print(month_av_temps_moyen_percentage(scenario, start_date, end_date))
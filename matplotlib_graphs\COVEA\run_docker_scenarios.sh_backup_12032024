#!/bin/bash

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 1 : Liste de comptes bancaires liés à un collaborateur" "2023-08-01" "2023-08-31" "COVEA"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 2 : Liste de rémunérations liées à un contrat" "2023-08-01" "2023-08-31" "COVEA"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 3 : Liste des métiers, domaines, contextes liés à un contrat" "2023-08-01" "2023-08-31" "COVEA"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 4 : Liste des horaires GTA" "2023-08-01" "2023-08-31" "COVEA"

docker run -v /data2/dockerized_rpa_v2/matplotlib_graphs/COVEA/pdfs:/app/output covea-automated-cmplx "PL - COVEA - Scénario 5 : Ma liste des soldes congés (Profil collaborateur Mes absences)" "2023-08-01" "2023-08-31" "COVEA"

